"use client";

import { useRouter } from "next/navigation";
import React, { useState, useEffect } from "react";
import { MagnifyingGlassIcon } from "@heroicons/react/20/solid";
import Select from "react-select";

import { Fragment } from "react";
import { Popover, Transition } from "@headlessui/react";
import { ChevronDownIcon } from "@heroicons/react/20/solid";
import { convertDateIntoIndianFormat } from "@/helpers/dateHelpers";
import Pagination from "@/components/Pagination/Pagination";

const classOptions = [
  { value: "class", label: "Class" },
  { value: "course", label: "Course" },
  { value: "", label: "All" },
];

const statusOptions = [
  { value: "Active", label: "Active" },
  { value: "Inactive", label: "Inactive" },
  { value: "", label: "All" },
];

const BookingList = () => {
  const router = useRouter();

  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);

  const [searchLoading, setSearchLoading] = useState(false);

  const [classFilter, setClassFilter] = useState();
  const [statusFilter, setStatusFilter] = useState();
  const [startDate, setStartDate] = useState();
  const [endDate, setEndDate] = useState();

  const [searchValue, setSearchValue] = useState("");
  const [totalResults, setTotalResults] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedPage, setSelectedPage] = useState(1);

  const getSearchResult = async ({
    search = "",
    clas = null,
    status = null,
    start = null,
    end = null,
  } = {}) => {
    try {
      // console.log(classFilter, statusFilter, "pp");
      const obj = {
        q: search,
        classType: clas ? clas?.value : classFilter?.value,
        status: status ? status?.value : statusFilter?.value,
        startDate: start ? start : startDate,
        endDate: end ? end : endDate,
        page: selectedPage,
      };
      // console.log("running", obj);
      setSearchLoading(true);
      setSearchValue(search);
      setStatusFilter(status);
      setClassFilter(clas);
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: JSON.stringify(obj),
      };
      const response = await fetch(`/api/booking_filter`, requestOptions);

      const result = await response.json();
      // console.log(result, "result here");
      setLoading(false);

      if (!result.error) {
        // console.log(result, "ppp");
        setCourses(result.data);
        setSearchLoading(false);
        setTotalResults(Number(result.totalResults));
        setTotalPages(Number(result.totalPages));
        setCurrentPage(Number(result.currentPage));
      }
    } catch (error) {
      console.log("error 90");
      setLoading(false);
      setSearchLoading(false);
    }
  };

  useEffect(() => {
    getSearchResult();
  }, [selectedPage, setSelectedPage]);

  return (
    <>
      <title>Coach booking - find all bookings here</title>
      {loading ? (
        <div className="flex items-center justify-center h-screen">
          <div
            className="inline-block h-20 w-20 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"
            role="status"
          >
            <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">
              Loading...
            </span>
          </div>
        </div>
      ) : (
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="mt-8 flow-root">
            <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
              <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8 min-h-[500px]">
                {/* Search bar */}

                <header className="mb-2">
                  <div className="mt-2 flex rounded-md shadow-sm">
                    <div className="relative flex flex-grow items-stretch focus-within:z-10">
                      <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                        <MagnifyingGlassIcon
                          className="h-5 w-5 text-gray-400"
                          aria-hidden="true"
                        />
                      </div>

                      <input
                        type="text"
                        name="search"
                        className="block w-full rounded-none rounded-l-md border-0 py-1.5 pl-10 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        id="search here"
                        onChange={(e) => {
                          setSearchValue(e.target.value);
                          if (
                            e.target.value.length >= 3 ||
                            e.target.value.length == 0
                          ) {
                            setSelectedPage(1);
                            setTimeout(() => {
                              getSearchResult({
                                search: e.target.value,
                                clas: classFilter,
                                status: statusFilter,
                              });
                            }, 500);
                          }
                        }}
                        placeholder="Search"
                      />
                      {searchLoading && (
                        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                          <svg
                            className="animate-spin h-5 w-5 text-black"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V2.55A9.93 9.93 0 003.45 12H4zm2 0a6 6 0 016-6V2.55A9.93 9.93 0 005.45 12H6zm10 0a2 2 0 100-4 2 2 0 000 4z"
                            ></path>
                          </svg>
                        </div>
                      )}
                    </div>

                    {/*Filters */}

                    <div className="flex justify-around">
                      <Popover className="relative p-2 border py-1.5 border-grey-600 bg-white rounded-none rounded-1-md hover:bg-slate-100 h-9.5">
                        <Popover.Button className="inline-flex items-center gap-x-1 text-sm font-semibold leading-6 text-grey-300">
                          <span>Date Range</span>
                          <ChevronDownIcon
                            className="h-5 w-5"
                            aria-hidden="true"
                          />
                        </Popover.Button>

                        <Transition
                          as={Fragment}
                          enter="transition ease-out duration-200"
                          enterFrom="opacity-0 translate-y-1"
                          enterTo="opacity-100 translate-y-0"
                          leave="transition ease-in duration-150"
                          leaveFrom="opacity-100 translate-y-0"
                          leaveTo="opacity-0 translate-y-1"
                        >
                          <Popover.Panel className="absolute left-1/2 z-10 mt-5 flex w-screen max-w-max -translate-x-1/2 px-4">
                            <div className="w-screen max-w-sm flex-auto rounded-3xl bg-white p-4 text-sm leading-6 shadow-lg ring-1 ring-gray-900/5">
                              <div>
                                <div>
                                  <label
                                    htmlFor="start_date"
                                    className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                                  >
                                    Start Date
                                  </label>
                                  <div className="mt-2">
                                    <input
                                      type="date"
                                      name="start_date"
                                      value={startDate}
                                      id="start_date"
                                      onChange={(e) => {
                                        // console.log(e.target.value, "iiii");
                                        setSelectedPage(1);
                                        setStartDate(e.target.value);
                                        getSearchResult({
                                          search: searchValue,
                                          status: statusFilter,
                                          clas: classFilter,
                                          start: e.target.value,
                                          end: endDate,
                                        });
                                      }}
                                      className="block p-3 w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                      // placeholder="<EMAIL>"
                                    />
                                  </div>
                                </div>

                                <div>
                                  <label
                                    htmlFor="end_date"
                                    className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                                  >
                                    End Date
                                  </label>
                                  <div className="mt-2">
                                    <input
                                      type="date"
                                      name="end_date"
                                      value={endDate}
                                      id="end_date"
                                      onChange={(e) => {
                                        // console.log(e.target.value, "iiii");
                                        setSelectedPage(1);
                                        setEndDate(e.target.value);
                                        getSearchResult({
                                          search: searchValue,
                                          status: statusFilter,
                                          clas: classFilter,
                                          start: startDate,
                                          end: e.target.value,
                                        });
                                      }}
                                      className="block p-3 w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                      // placeholder="<EMAIL>"
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          </Popover.Panel>
                        </Transition>
                      </Popover>

                      <Select
                        onChange={(value) => {
                          setClassFilter(value);
                          setSelectedPage(1);
                          getSearchResult({
                            search: searchValue,
                            status: statusFilter,
                            clas: value,
                            start: startDate,
                            end: endDate,
                          });

                          // console.log(value);
                        }}
                        options={classOptions}
                        isSearchable={false}
                        placeholder={"Class Type"}
                      />

                      <Select
                        options={statusOptions}
                        onChange={(value) => {
                          setStatusFilter(value);
                          setSelectedPage(1);
                          getSearchResult({
                            search: searchValue,
                            status: value,
                            clas: classFilter,
                            start: startDate,
                            end: endDate,
                          });
                          // console.log(value);
                        }}
                        isSearchable={false}
                        placeholder={"Status Type"}
                      />
                    </div>
                  </div>
                </header>

                {/* Table */}
                <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                  <table className="min-w-full divide-y divide-gray-300">
                    <thead className="bg-gray-50">
                      <tr>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Booking Id
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Training Schedule Name
                        </th>
                        {/* <th
                      scope="col"
                      className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                    >
                      Players
                    </th> */}
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Training Schedule Type
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Amount
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Booking Date
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Athlete
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 bg-white">
                      {courses &&
                        courses.length > 0 &&
                        courses?.map((course, idx) => (
                          <tr
                            className="cursor-pointer"
                            key={course._id}
                            onClick={() => {
                              router.push(`/bookings/${course?._id}`);
                            }}
                          >
                            <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                              {course?.bookingId}
                            </td>
                            <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                              {course?.courseId?.courseName}
                            </td>

                            <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                              {course?.courseType}
                            </td>
                            <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                              ₹
                              {Math.ceil(
                                course.classes
                                  .reduce((acc, curr) => acc + curr.fees, 0)
                                  .toFixed(2)
                              )}
                            </td>
                            <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                              {convertDateIntoIndianFormat(
                                new Date(course?.createdAt.split("T")[0])
                              )}
                            </td>
                            <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                              {course?.player?.firstName}
                            </td>
                            {course.status == "Active" && (
                              <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                <span className="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">
                                  Active
                                </span>
                              </td>
                            )}

                            {course.status == "Inactive" && (
                              <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-inset ring-red-600/20">
                                  Inactive
                                </span>
                              </td>
                            )}
                          </tr>
                        ))}
                    </tbody>
                  </table>
                </div>
                {courses && courses.length > 0 && (
                  <Pagination
                    totalResults={totalResults}
                    currentPage={currentPage}
                    totalPages={totalPages}
                    setSelectedPage={setSelectedPage}
                    selectedPage={selectedPage}
                  />
                )}
                {!(courses && courses.length > 0) && (
                  <span className="flex justify-center mt-2">
                    No result found.
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default BookingList;
