"use client";
import { useRouter } from "next/router";
import SideBar from "../SideBar/SideBar";
import Header from "../Header/Header";
import { useState } from "react";
import { usePathname } from "next/navigation";

export default function Layout({ children }) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const pathname = usePathname();
  if (pathname === "/login" || pathname === "/signup") {
    return <div className="flex w-full">{children}</div>;
  } else {
    return (
      <div className="flex w-full">
        <SideBar sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />
        <div className="rightSide removeScroller w-full h-screen overflow-auto border-l bg-slate-50">
          <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />
          <div
            className="p-4 xl:px-8 xl:py-6 bg-slate-50 h-screen"
            style={{ padding: "0" }}
          >
            {children}
          </div>
        </div>
      </div>
    );
  }
}
