"use client";
import Link from "next/link";
import Image from "next/image";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useState } from "react";
import { useRouter } from "next/navigation";
// import { signIn } from "next-auth/react";
// import { useSession } from "next-auth/react";

const emailRegExp = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
const signInSchema = Yup.object({
  email: Yup.string()
    .email()
    .matches(emailRegExp, "Email is not valid")
    .required("Please enter your email"),
  password: Yup.string()
    .min(6, "Password must be at least 8 characters")
    .required("Please enter your password"),
});
const initialValues = {
  email: "",
  password: "",
  remember: false,
};
export default function Login() {
  const [userNotRegistered, setUserNotRegistered] = useState("");
  const [invalidCredentials, setInvalidCredentials] = useState("");
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const router = useRouter();

  // const session = useSession()

  // console.log(session)

  const { values, errors, touched, handleBlur, handleChange, handleSubmit } =
    useFormik({
      initialValues: initialValues,
      validationSchema: signInSchema,
      onSubmit: async (values) => {
        try {
          setLoading(true);
          let myHeaders = new Headers();
          myHeaders.append("Content-Type", "application/json");
          let requestOptions = {
            method: "POST",
            headers: myHeaders,
            body: JSON.stringify(values),
            redirect: "follow",
          };
          const response = await fetch(`/api/login`, requestOptions);
          const result = await response.json();
          // console.log(result, "result");
          if (result.error) {
            setInvalidCredentials("Invalid Credentials");
            setLoading(false);
          }
          if (result.success == true) {
            setInvalidCredentials("");
            setUserNotRegistered("");
            router.push(`/dashboard`);
            setLoading(false);
            window.location.reload();
          }
        } catch (error) {
          console.log("error 68");
          setLoading(false);
          setInvalidCredentials(
            "An error occurred during login. Please try again."
          );
        }
      },
    });

  return (
    <>
    <title>Coach Login</title>
      <div className="flex min-h-full flex-1 flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="mt-2 sm:mx-auto sm:w-full sm:max-w-[480px]">
          <div className="bg-white px-6 py-12 shadow sm:rounded-lg sm:px-12">
            <div className="sm:mx-auto sm:w-full sm:max-w-sm">
              <Image
                priority
                className="mx-auto h-15 md:w-[152px] w-[104px]"
            src="/main_logo.png"
                alt="Your Company"
                width={100}
                height={100}
              />
              <h2 className="mt-6 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900">
                Sign in to your account
              </h2>
            </div>
            <br />
            <br />
            <form
              className="space-y-6"
              action="#"
              method="POST"
              onSubmit={handleSubmit}
            >
              <div>
                <label
                  htmlFor="email"
                  className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                >
                  Email address
                </label>
                <div className="mt-2">
                  <input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="Please enter the email"
                    autoComplete="email"
                    value={values.email}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className={`block px-3 w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6
                      ${
                        (errors.email && touched.email) ||
                        userNotRegistered ||
                        invalidCredentials
                          ? "border-2 border-rose-500"
                          : ""
                      }`}
                  />
                  {(errors.email && touched.email) || userNotRegistered ? (
                    <span className="form_errors form-email text-sm font-medium leading-6 text-red-500">
                      {userNotRegistered ? userNotRegistered : errors.email.charAt(0).toUpperCase() + errors.email.slice(1)}
                    </span>
                  ) : null}
                </div>
              </div>

              <div>
                <label
                  htmlFor="password"
                  className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                >
                  Password
                </label>
                <div className="mt-2">
                  <div className="flex flex-row">
                    <input
                      id="password"
                      name="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter the password"
                      autoComplete="current-password"
                      value={values.password}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      className={`px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6
                    ${
                      (errors.password && touched.password) ||
                      invalidCredentials
                        ? "border-2 border-rose-500"
                        : ""
                    }`}
                    />
                    <Image
                      height={20}
                      width={20}
                      style={{ marginLeft: "-30px" }}
                      src={showPassword ? "showEye.svg" : "/hideEye.svg"}
                      alt=""
                      onClick={() => {
                        setShowPassword(!showPassword);
                      }}
                    />
                  </div>

                  {(errors.password && touched.password) ||
                  invalidCredentials ? (
                    <span className="form_errors form-password text-sm font-medium leading-6 text-red-500">
                      {invalidCredentials
                        ? invalidCredentials
                        : errors.password}
                    </span>
                  ) : null}
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <p className="text-center text-sm text-gray-500">
                    Not a member?{" "}
                    <Link
                      href="/coach_profile/CoachProfileCreate"
                      as="/coach_profile/CoachProfileCreate"
                      className="font-semibold leading-6 text-sky-500 hover:text-sky-600"
                    >
                      Sign Up
                    </Link>
                  </p>
                </div>

                <div className="text-sm leading-6">
                  <Link
                    href="/forgot_password"
                    className="font-semibold text-sky-500 hover:text-sky-600"
                  >
                    Forgot password?
                  </Link>
                </div>
              </div>
              <div>
                <button
                  type="submit"
                  disabled={loading}
                  className="flex w-full justify-center rounded-md bg-red-500 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-red-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                >
                  {loading ? (
                    <svg
                      aria-hidden="true"
                      class="w-8 h-8 text-gray-200 animate-spin dark:text-white fill-blue-600"
                      viewBox="0 0 100 101"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                        fill="currentColor"
                      />
                      <path
                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                        fill="currentFill"
                      />
                    </svg>
                  ) : (
                    "Login"
                  )}
                </button>
              </div>
            </form>

            {/* <button onClick={() => signIn("google")}>signin with google</button> */}
            {/* <div>
              <div className="relative mt-10">
                <div
                  className="absolute inset-0 flex items-center"
                  aria-hidden="true"
                >
                  <div className="w-full border-t border-gray-200" />
                </div>
                <div className="relative flex justify-center text-sm font-medium leading-6">
                  <span className="bg-white px-6 text-gray-900">Or</span>
                </div>
              </div>

              <div className="mt-6 ">
                <div>
                  <button
                    type="submit"
                    className="flex w-full justify-center rounded-md bg-red-500 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-red-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                  >
                    Continue With OTP
                  </button>
                </div>
              </div>
            </div> */}
          </div>
        </div>
      </div>
    </>
  );
}
