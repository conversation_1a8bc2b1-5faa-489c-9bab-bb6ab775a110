"use client";
import { Fragment, useState, useRef, useEffect } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { Calendar } from "react-multi-date-picker";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "./datePicker.css";
import { v4 as uuidv4 } from "uuid";

export default function SpecificClasses({
  setSelectedDatesAndTime,
  dates,
  setClassPrice,
  deleteHandler,
  datesError,
  coursePrice,
  coursePriceError2,
}) {
  const [open, setOpen] = useState(false);
  const cancelButtonRef = useRef(null);
  const datePickerRef = useRef(null);

  const [selectedDates, setSelectedDates] = useState([]);
  const [timeFrom, setTimeFrom] = useState("");
  const [timeTo, setTimeTo] = useState("");
  const [excludedSlots, setExcludedSlots] = useState([]);
  const [timeToError, setTimeToError] = useState(false);
  const [coursePriceError, setCoursePriceError] = useState(false);

  const addSessionHandler = () => {
    setOpen(true);
    setTimeFrom("");
    setTimeTo("");
    setSelectedDates([]);
    setTimeToError(false);
  };

  const getCurrentTime = () => {
    const currentDate = new Date();
    const currentHours = currentDate.getHours();
    const currentMinutes = currentDate.getMinutes();
    return new Date(0, 0, 0, currentHours, currentMinutes);
  };

  const currentTime = getCurrentTime();

  function getLastDateOfCurrentYear() {
    const today = new Date();
    const lastDayOfYear = new Date(today.getFullYear() + 1, 0, 0); // January 0 is the last day of the previous year

    const year = lastDayOfYear.getFullYear();
    let month = lastDayOfYear.getMonth() + 1;
    let day = lastDayOfYear.getDate();

    // Add leading zero if month or day is less than 10
    month = month < 10 ? `0${month}` : month;
    day = day < 10 ? `0${day}` : day;

    return `${year}-${month}-${day}`;
  }

  const includesToday = selectedDates.some((dateString) => {
    const date = new Date(dateString);
    const today = new Date();
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    );
  });

  const minTime = includesToday ? currentTime : undefined;

  //   functionality to hide product dropdown
  const handleClickOutside = (event) => {
    if (
      datePickerRef.current &&
      !datePickerRef.current.contains(event.target)
    ) {
      if (!event.target.tagName.toLowerCase().match(/input|label/)) {
        datePickerRef.current.closeCalendar();
      }
    }
  };
  // Dropdown useEffect
  useEffect(() => {
    // Bind the event listener
    document.addEventListener("mousedown", handleClickOutside);

    // Unbind the event listener on cleanup
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // timeslots disabled logic
  useEffect(() => {
    const getTimeSlotForDate = (date, from, to) => {
      const fromDate = new Date(date);
      const timeFrom = from.toUpperCase();
      const timeTo = to.toUpperCase();
      // Parse the time strings
      const [hoursFrom, minutesFrom] = timeFrom
        .match(/(\d+):(\d+) (\w+)/)
        .slice(1);
      const [hoursTo, minutesTo] = timeTo.match(/(\d+):(\d+) (\w+)/).slice(1);
      // Convert hours to 24-hour format
      const hoursFrom24 =
        hoursFrom === "12" && timeFrom.includes("AM")
          ? "00"
          : hoursFrom === "12" && timeFrom.includes("PM")
          ? "12"
          : timeFrom.includes("PM")
          ? parseInt(hoursFrom, 10) + 12
          : parseInt(hoursFrom, 10);

      const hoursTo24 =
        hoursTo === "12" && timeTo.includes("AM")
          ? "00"
          : hoursTo === "12" && timeTo.includes("PM")
          ? "12"
          : timeTo.includes("PM")
          ? parseInt(hoursTo, 10) + 12
          : parseInt(hoursTo, 10)

      // Set the time in the fromDate and toDate objects
      fromDate.setHours(hoursFrom24, minutesFrom, 0, 0);
      const toDate = new Date(fromDate);
      toDate.setHours(hoursTo24, minutesTo, 0, 0);

      const timeSlots = [];

      // Format date as "YYYY-MM-DD"
      function dateConverterX(date_x) {
        let inputDate = new Date(date_x);

        let formattedDateString = inputDate.toDateString();
        let inputDateString = formattedDateString;
        let inputDateX = new Date(inputDateString);

        let year = inputDateX.getFullYear();
        let month = ("0" + (inputDateX.getMonth() + 1)).slice(-2); // Adding 1 because months are zero-based
        let day = ("0" + inputDateX.getDate()).slice(-2);

        let isoDateString = year + "-" + month + "-" + day;
        return isoDateString;
      }
      const formattedDate = dateConverterX(fromDate);
      const timeSlotObject = {
        date: formattedDate,
        timeSlots: [],
      };

      // Iterate through the time slots with a 15-minute interval
      while (fromDate <= toDate) {
        timeSlotObject.timeSlots.push(fromDate.toString());
        fromDate.setMinutes(fromDate.getMinutes() + 15);
      }

      timeSlots.push(timeSlotObject);

      return timeSlots;
    };

    const datesAndTime = selectedDates?.map((date) => {
      let format = new Date(date);
      format.setUTCHours(0, 0, 0, 0);
      return format.toISOString().slice(0, 10);
    });

    let excludeLogic = dates.map((x) =>
      getTimeSlotForDate(x.date, x.timeFrom, x.timeTo)
    );

    excludeLogic = excludeLogic.flat(Infinity);

    let filterTimeSlotsForDate = excludeLogic.filter((x) =>
      datesAndTime.find((y) => y === x.date)
    );

    let finalFilterTimeSlots = filterTimeSlotsForDate.map((x) => x.timeSlots);

    if (typeof timeFrom === "object") {
      let timeFromParse = new Date(timeFrom);
      finalFilterTimeSlots.push(timeFromParse);
    }

    setExcludedSlots(
      finalFilterTimeSlots.flat(Infinity).map((x) => new Date(x))
    );
  }, [selectedDates, timeFrom]);

  return (
    <div className="bg-gray-100 p-4 rounded-sm  grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
      {/* Price */}
      <div className="col-span-full">
        <label
          htmlFor="course-price"
          className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize tracking-wide"
        >
          Price
        </label>
        <div className="mt-1">
          <input
            type="number"
            name="course-price"
            id="course-price"
            className={
              coursePriceError2 || coursePriceError
                ? "mt-1 block w-full px-3 py-2  rounded-md border-2 border-rose-500  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                : "mt-1 block w-full px-3 py-2  rounded-md border-0  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
            }
            placeholder="Enter price"
            value={coursePrice}
            onChange={(e) => {
              setClassPrice(e.target.value.replace(/\s+/g, " ").trim());
              if (e.target.value !== "") {
                setCoursePriceError(false);
              } else {
                setCoursePriceError(true);
              }
            }}
          />
          {(coursePriceError2 || coursePriceError) && (
            <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
              Please enter course price
            </span>
          )}
        </div>
      </div>

      <div className="col-span-full">
        <div className="w-full flex flex-col justify-between justify-items-center">
          <button
            type="button"
            className="w-full rounded bg-black px-6 py-2 text-sm  text-white shadow-sm hover:bg-slate-950 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-slate-950"
            onClick={addSessionHandler}
          >
            Add Session
          </button>
          {datesError && (
            <span className="mt-0 text-md text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
              Please add sessions
            </span>
          )}
        </div>
        {/* Table */}
        <div className="px-4 sm:px-2 lg:px-0">
          <div className="mt-3 flow-root">
            <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
              <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                  <div className="max-h-80 h-auto overflow-y-scroll">
                    <table className="min-w-full h-auto overflow-y-scroll divide-y divide-gray-300">
                      <thead className="bg-gray-50">
                        <tr>
                          <th
                            scope="col"
                            className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 "
                          >
                            S.No
                          </th>
                          <th
                            scope="col"
                            className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                          >
                            Date
                          </th>
                          <th
                            scope="col"
                            className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                          >
                            From
                          </th>
                          <th
                            scope="col"
                            className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                          >
                            To
                          </th>
                          <th
                            scope="col"
                            className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                          >
                            Action
                          </th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200 bg-white">
                        {dates.map((x, i) => (
                          <tr  key={x.id} className="divide-x divide-gray-200">
                            <td className="whitespace-nowrap py-4 pl-4 pr-4 text-sm font-medium text-center text-gray-900 sm:pl-0">
                              {i + 1}
                            </td>
                            <td className="whitespace-nowrap p-4 text-sm text-gray-500">
                              {x?.date}
                            </td>
                            <td className="whitespace-nowrap p-4 text-sm text-gray-500">
                              {x?.timeFrom}
                            </td>
                            <td className="whitespace-nowrap p-4 text-sm text-gray-500">
                              {x?.timeTo}
                            </td>
                            <td className="whitespace-nowrap py-4 pl-6 text-center pr-4 text-sm text-gray-500 sm:pr-0">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                strokeWidth={1.5}
                                stroke="currentColor"
                                className="w-5 h-5 cursor-pointer"
                                onClick={() => deleteHandler(x?.id)}
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                                />
                              </svg>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Add Session Modal */}
      <Transition.Root show={open} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-10"
          initialFocus={cancelButtonRef}
          onClose={setOpen}
        >
          <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
            <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                enterTo="opacity-100 translate-y-0 sm:scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              >
                <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                  <div>
                    <div className="text-center">
                      <h1 className="text-xl font-medium tracking-wide">
                        Add Session
                      </h1>
                    </div>
                    <div className="mt-3 flex flex-row justify-between justify-items-center">
                      <div className="basis-7/12">
                        <Calendar
                          value={selectedDates}
                          onChange={(value) => {
                            setSelectedDates(value);
                            setTimeFrom("");
                            setTimeTo("");
                          }}
                          minDate={new Date()}
                          maxDate={getLastDateOfCurrentYear()}
                        />
                      </div>
                      <div className="basis-2/5">
                        <div>
                          <h1 className="text-lg font-medium tracking-wide">
                            From:
                          </h1>
                          <DatePicker
                            selected={timeFrom}
                            onChange={(value) => {
                              setTimeFrom(value);
                              setTimeTo("");
                            }}
                            showTimeSelect
                            showTimeSelectOnly
                            timeIntervals={15}
                            timeCaption="Time"
                            dateFormat="h:mm aa"
                            className="my-custom-datepicker"
                            popperPlacement="left-start"
                            placeholderText="Select time"
                            disabled={selectedDates.length === 0}
                            minTime={minTime || new Date(0, 0, 0, 0, 0)}
                            maxTime={new Date(0, 0, 0, 23, 59)}
                            excludeTimes={excludedSlots || []}
                          />
                        </div>
                        <div
                          className={
                            timeFrom.length !== 0
                              ? "mt-4"
                              : "mt-4 pointer-events-none"
                          }
                        >
                          <h1 className="text-lg font-medium tracking-wide">
                            To:
                          </h1>
                          <DatePicker
                            selected={timeTo}
                            onChange={(value) => {
                              function checkExcludedTime(
                                timeFrom,
                                value,
                                excludedSlots
                              ) {
                                // Convert timeFrom and timeTo to timestamps for easy comparison

                                let x_from = new Date(timeFrom);
                                x_from =
                                  x_from.getHours() * 3600 +
                                  x_from.getMinutes() * 60 +
                                  x_from.getSeconds();

                                let x_to = new Date(value);
                                x_to =
                                  x_to.getHours() * 3600 +
                                  x_to.getMinutes() * 60 +
                                  x_to.getSeconds();

                                let excludeds = excludedSlots.map(
                                  (x) => new Date(x)
                                );

                                let filterExcludes = excludeds.filter(
                                  (y) =>
                                    new Date(y).getTime() !==
                                    new Date(timeFrom).getTime()
                                );

                                const final_exclude = filterExcludes.map(
                                  (n) => new Date(n)
                                );

                                // Check for excluded time slots

                                if (final_exclude.length > 0) {
                                  for (const filterExclude of final_exclude) {
                                    const excludedTimestamp =
                                      filterExclude.getHours() * 3600 +
                                      filterExclude.getMinutes() * 60 +
                                      filterExclude.getSeconds();
                                    // If there is an excluded time slot between timeFrom and timeTo, throw an error
                                    if (
                                      excludedTimestamp >= x_from &&
                                      excludedTimestamp <= x_to
                                    ) {
                                      setTimeTo("");
                                      setTimeToError(true);
                                    } else {
                                      setTimeTo(value);
                                      setTimeToError(false);
                                    }
                                  }
                                } else {
                                  setTimeTo(value);
                                  setTimeToError(false);
                                }
                              }
                              checkExcludedTime(timeFrom, value, excludedSlots);
                            }}
                            showTimeSelect
                            showTimeSelectOnly
                            timeIntervals={15}
                            timeCaption="Time"
                            dateFormat="h:mm aa"
                            className={
                              timeToError
                                ? "my-custom-datepicker dateError"
                                : "my-custom-datepicker"
                            }
                            popperPlacement="left-start"
                            placeholderText="Select time"
                            disabled={selectedDates.length === 0}
                            minTime={timeFrom}
                            maxTime={new Date(0, 0, 0, 23, 59)}
                            excludeTimes={excludedSlots || []}
                          />
                          {timeToError && (
                            <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                              Select different time slot
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                    <button
                      type="button"
                      className="inline-flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 sm:col-start-2"
                      onClick={() => {
                        const datesAndTime = selectedDates?.map((date) =>
                          date.toLocaleString("en-IN")
                        );

                        const timeConverter = (data) => {
                          const currentDate = new Date(data);
                          const options = {
                            hour: "numeric",
                            minute: "numeric",
                            hour12: true,
                          };
                          return currentDate.toLocaleTimeString(
                            "en-IN",
                            options
                          );
                        };
                        const finalDateWithTime = datesAndTime.map((x) => {
                          return {
                            id: uuidv4(),
                            date: x,
                            duration: "",
                            timeFrom: timeConverter(timeFrom),
                            timeTo: timeConverter(timeTo),
                          };
                        });
                        if (timeConverter(timeFrom) === timeConverter(timeTo)) {
                          setOpen(true);
                        } else {
                          setSelectedDatesAndTime(finalDateWithTime);
                          setOpen(false);
                          setTimeFrom("");
                          setTimeTo("");
                        }
                      }}
                    >
                      Save
                    </button>
                    <button
                      type="button"
                      className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:col-start-1 sm:mt-0"
                      onClick={() => {
                        setOpen(false);
                        setSelectedDates([]);
                        setTimeFrom("");
                        setTimeTo("");
                      }}
                      ref={cancelButtonRef}
                    >
                      Cancel
                    </button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition.Root>
    </div>
  );
}
