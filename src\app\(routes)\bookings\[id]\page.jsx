"use client";

import React from "react";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import Link from "next/link";
import {
  convertDateIntoIndianFormat,
  convertTime,
} from "@/helpers/dateHelpers";

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

const BookingDetails = () => {
  const params = useParams();

  const bookingId = params.id;

  function calculate18Percent(amount) {
    return amount * 0.18;
  }

  function calculate10Percent(amount) {
    return amount * 0.1;
  }

  const [bookingData, setBookingData] = useState({});
  const [loading, setLoading] = useState(true);

  const getBookingDetails = async () => {
    try {
      setLoading(true);
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: JSON.stringify(bookingId),
      };
      const response = await fetch(`/api/bookings`, requestOptions);

      const result = await response.json();
      if (!result.error) {
        setBookingData(result);
        // console.log(result, "booking data");
        setLoading(false);
      }
    } catch (error) {
      console.log("error 52");
      setLoading(false);
    }
  };

  useEffect(() => {
    getBookingDetails();
  }, [bookingId]);

  return (
    <>
      <title>Bookings detail page </title>

      {loading ? (
        <div className="flex items-center justify-center h-screen">
          <div
            className="inline-block h-20 w-20 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"
            role="status"
          >
            <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">
              Loading...
            </span>
          </div>
        </div>
      ) : (
        <div className="bg-gray-50">
          <div className="mx-auto max-w-2xl pt-16 sm:px-6 sm:py-24 lg:max-w-7xl lg:px-8">
            <div className="space-y-2 px-4 sm:flex sm:items-baseline sm:justify-between sm:space-y-0 sm:px-0">
              <div className="flex sm:items-baseline sm:space-x-4">
                <p className="text-3xl font-bold tracking-tight text-gray-900 sm:text-xl">
                  Booking Id - #{bookingData.bookingId}{" "}
                  {bookingData.status == "Inactive" && (
                    <span className="inline-flex items-center rounded-md bg-ewd-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-inset ring-red-600/20">
                      Inactive
                    </span>
                  )}
                  {bookingData.status == "Active" && (
                    <span className="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">
                      Active
                    </span>
                  )}
                </p>
              </div>
              <p className="text-3xl font-bold tracking-tight text-gray-900 sm:text-xl">
                Booked On &nbsp;
                <time
                  dateTime={convertDateIntoIndianFormat(
                    new Date(bookingData.createdAt.split("T")[0])
                  )}
                  className="text-sm text-gray-600"
                >
                  {bookingData?.createdAt &&
                    convertDateIntoIndianFormat(
                      new Date(bookingData.createdAt.split("T")[0])
                    )}
                </time>
              </p>
              {/* <a
            href="#"
            className="text-sm font-medium text-indigo-600 hover:text-indigo-500 sm:hidden"
          >
            View invoice
            <span aria-hidden="true"> &rarr;</span>
          </a> */}
            </div>

            {/* Products */}
            <div className="mt-6">
              <h2 className="sr-only">Products purchased</h2>

              <div className="space-y-8">
                <div className="border-b border-t border-gray-200 bg-white shadow-sm sm:rounded-lg sm:border">
                  <div className="px-4 py-6 sm:px-6 lg:grid lg:grid-cols-12 lg:gap-x-8 lg:p-8">
                    <div className="sm:flex lg:col-span-12">
                      <div className="aspect-h-1 aspect-w-1 w-full flex-shrink-0 overflow-hidden rounded-lg sm:aspect-none sm:h-40 sm:w-40">
                        <img
                          src={bookingData?.courseId?.images[0]?.url}
                          alt={"Course Image"}
                          className="h-full w-full object-cover object-center sm:h-full sm:w-full"
                        />
                      </div>

                      <div className="mt-6 sm:ml-6 sm:mt-0">
                        Course Name:- &nbsp;
                        <Link
                          href={`/course/create/?coach=${bookingData.courseId?._id}`}
                          className="text-base font-medium text-blue-900"
                        >
                          {bookingData?.courseId?.courseName}
                        </Link>
                        <p className="mt-2 text-sm font-medium text-gray-900">
                          Course Price:-&nbsp; ₹{" "}
                          {Math.ceil(
                            bookingData.classes
                              .reduce((acc, curr) => acc + curr.fees, 0)
                              .toFixed(2)
                          )}
                        </p>
                        <p
                          className="mt-3 text-sm text-gray-500"
                          dangerouslySetInnerHTML={{
                            __html: bookingData?.courseId?.description,
                          }}
                        ></p>
                      </div>
                    </div>

                    <div className="mt-6 lg:col-span-12 lg:mt-0">
                      <div className="px-4 sm:px-6 lg:px-8">
                        <div className="mt-8 flow-root">
                          <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                            <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                              <table className="min-w-full divide-y divide-gray-300">
                                <thead>
                                  <tr>
                                    <th
                                      scope="col"
                                      className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                    >
                                      Start Date
                                    </th>
                                    <th
                                      scope="col"
                                      className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                    >
                                      Start Time
                                    </th>
                                    <th
                                      scope="col"
                                      className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                    >
                                      End Time
                                    </th>
                                    <th
                                      scope="col"
                                      className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                    >
                                      Duration
                                    </th>
                                    <th
                                      scope="col"
                                      className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                    >
                                      Status
                                    </th>
                                  </tr>
                                </thead>
                                <tbody className="divide-y divide-gray-200">
                                  {bookingData?.classes?.map((date, idx) => (
                                    <tr key={idx}>
                                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                        {convertDateIntoIndianFormat(
                                          new Date(date?.date)
                                        )}
                                      </td>
                                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                        {convertTime(date.startTime)}
                                      </td>
                                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                        {convertTime(date.endTime)}
                                      </td>
                                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                        {date.duration}
                                      </td>
                                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                        {date.status == "completed" && (
                                          <span className="inline-flex items-center rounded-md bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20">
                                            Completed
                                          </span>
                                        )}
                                        {date.status == "cancelled" && (
                                          <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-inset ring-red-600/20">
                                            Cancelled
                                          </span>
                                        )}
                                        {date.status == "upcoming" && (
                                          <span className="inline-flex items-center rounded-md bg-yellow-50 px-2 py-1 text-xs font-medium text-yellow-700 ring-1 ring-inset ring-yellow-600/20">
                                            Upcoming
                                          </span>
                                        )}
                                        {date.status == "rescheduled" && (
                                          <span className="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-600/20">
                                            In Progress
                                          </span>
                                        )}
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Billing */}
            <div className="mt-16">
              {/* <h2 className="sr-only">Billing Summary</h2> */}

              <div className="bg-gray-100 px-4 py-6 sm:rounded-lg sm:px-6 lg:grid lg:grid-cols-12 lg:gap-x-8 lg:px-8 lg:py-8">
                <dl className="grid grid-cols-2 gap-6 text-sm sm:grid-cols-2 md:gap-x-8 lg:col-span-7 max-sm:flex max-sm:flex-col">
                  <div>
                    <dt className="font-medium text-black">
                      Player Information
                    </dt>
                    <dd className="mt-3 text-gray-500">
                      <span className="block">
                        {bookingData?.player?.firstName}{" "}
                        {bookingData?.player?.lastName}
                      </span>
                      <span className="block">
                        {bookingData?.player?.mobile}
                      </span>
                      <span className="block">
                        {bookingData?.player?.email}
                      </span>
                    </dd>
                    <br />

                    <dt className="font-medium text-gray-900">
                      Venue Information
                    </dt>
                    <dd className="mt-3 text-gray-500">
                      <span className="block">
                        {bookingData?.courseId?.facility?.name}
                      </span>
                      <span className="block mt-1">
                        {bookingData?.courseId?.facility?.addressLine1}
                        ,&nbsp;&nbsp;
                        {bookingData?.courseId?.facility?.addressLine2}
                      </span>
                      <span className="block mt-1">
                        {bookingData?.courseId?.facility?.city},
                        {bookingData?.courseId?.facility?.state}
                      </span>
                    </dd>
                  </div>
                  <div>
                    <dt className="font-medium text-black">
                      Payment information
                    </dt>
                    <dd className="-ml-4 -mt-1 flex flex-wrap">
                      <div className="ml-4 mt-4">
                        <p className="text-gray-500">
                          Payment Method:-{" "}
                          {bookingData?.paymentMode && bookingData?.wallet
                            ? `${bookingData?.paymentMode.toUpperCase()} & Wallet`
                            : bookingData?.paymentMode && !bookingData?.wallet
                            ? bookingData?.paymentMode.toUpperCase()
                            : "Wallet"}
                        </p>
                        <p className="text-gray-500 mt-2">
                          Payment Id:-{" "}
                          {bookingData?.razorPayPaymentId
                            ? bookingData?.razorPayPaymentId?.toUpperCase()
                            : bookingData?.bookingId}
                        </p>
                      </div>
                    </dd>
                  </div>
                </dl>

                <dl className="mt-8 divide-y divide-gray-200 text-sm lg:col-span-5 lg:mt-0">
                  {/* <div className="flex items-center justify-between pb-4">
                <dt className="text-gray-600">Subtotal</dt>
                <dd className="font-medium text-gray-900">$72</dd>
              </div> */}
                  <div className="flex items-center justify-between py-4">
                    <dt className="text-gray-600">Course Price</dt>
                    <dd className="font-medium text-gray-900">
                      ₹
                      {Math.ceil(
                        bookingData.classes
                          .reduce((acc, curr) => acc + curr.fees, 0)
                          .toFixed(2)
                      )}
                    </dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default BookingDetails;
