import axios from "axios";
import { NextResponse } from "next/server";
import { verifyToken } from "@/helpers/getDataFromToken";
import API from "@/components/API";

export async function POST(req) {
  try {
    let reqBody = await req.json();

    const eventId = reqBody.eventId;

    const token = req.cookies.get("coach-token")?.value || "";

    const payload = await verifyToken(token);

    const response = await axios.delete(`${API}/api/events/${payload.id}`, {
      data: {
        eventId: eventId.split("_")[0],
      },
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    return new NextResponse(JSON.stringify(response.status));
  } catch (error) {
    console.log(error);
    return new NextResponse(JSON.stringify({ error_add: error }));
  }
}
