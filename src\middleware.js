import { NextResponse } from "next/server";
import API from "./components/API";
// This function can be marked `async` if using `await` inside

export async function middleware(request) {
  const token = request.cookies.get("coach-token")?.value || "";
  const id = request.cookies.get("id")?.value || "";

  let verified = false;
  let authorize = false;

  try {
    if (token && id) {
      const response = await fetch(`${API}/api/coach/${id}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      const responseData = await response.json();
      if (responseData.status === "active") {
        verified = true;
      }
      if (responseData.authStatus === "authorized") {
        authorize = true;
      }
    }
  } catch (error) {
    console.log(error);
    return NextResponse.redirect(
      new URL("/profile/basic_details", request.nextUrl)
    );
  }

  if (request.nextUrl.pathname.startsWith("/login") && token) {
    return NextResponse.redirect(new URL("/dashboard", request.nextUrl));
  }
  if (!request.nextUrl.pathname.startsWith("/login") && !token) {
    return NextResponse.redirect(new URL("/login", request.nextUrl));
  }

  if (
    token &&
    !request.nextUrl.pathname.startsWith("/profile/professional_details") &&
    !authorize
  ) {
    return NextResponse.redirect(
      new URL("/profile/professional_details", request.nextUrl)
    );
  }

  if (
    token &&
    authorize &&
    !request.nextUrl.pathname.startsWith("/profile/kyc_details") &&
    !verified
  ) {
    return NextResponse.redirect(
      new URL("/profile/kyc_details#signup", request.nextUrl)
    );
  }
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: [
    "/",
    "/calendar",
    "/dashboard",
    "/login",
    "/signup",
    "/course/:path*",
    "/profile/kyc_details",
    "/profile/professional_details",
  ],
};
