"use client";

import { Fragment, useEffect, useRef, useState } from "react";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/20/solid";
import { Transition, Dialog } from "@headlessui/react";
import SuccessNotification from "@/components/Notification/SuccessNotification";
import ErrorNotification from "@/components/Notification/ErrorNotification";
import Calendar from "react-calendar";
import "react-calendar/dist/Calendar.css";
import Link from "next/link";
import "@/style/customStyle.css";
import moment from "moment-timezone";
import "../../components/CourseCreation/datePicker.css";
import DatePicker from "react-datepicker";

export default function Example() {
  const container = useRef(null);
  const containerNav = useRef(null);
  const containerOffset = useRef(null);

  const [open, setOpen] = useState(false);
  const [selectedDay, setSelectedDay] = useState([]);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [googleEventId, setGoogleEventId] = useState("");
  const [isEnd, setIsEnd] = useState(false);
  const [slide, setSlide] = useState(false);
  const [successNotification, setSuccessNotification] = useState(false);
  const [errorNotification, setErrorNotification] = useState(false);
  const [message, setMessage] = useState("Task added successfully");
  const [loading, setLoading] = useState(false);

  const [taskName, setTaskName] = useState("");
  const [taskStartDate, setTaskStartDate] = useState("");
  const [startTime, setStartTime] = useState("");
  const [endTime, setEndTime] = useState("");

  const daysOfWeek = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ];
  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  function formatDateToYYYYMMDD(dateString) {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = `0${date.getMonth() + 1}`.slice(-2); // Add leading zero if needed
    const day = `0${date.getDate()}`.slice(-2); // Add leading zero if needed

    return `${year}-${month}-${day}`;
  }

  const [currentDate, setCurrentDate] = useState(new Date());

  const [allEvents, setAllEvents] = useState([]);

  const calculateEvents = (event) => {
    let hours =
      new Date(event?.start?.dateTime.split("+")[0]).getHours() * 12 + 2;
    let minutes =
      new Date(event?.start?.dateTime.split("+")[0]).getMinutes() * 0.2;

    let start = Math.round(hours + minutes);

    let timeDifference = Math.abs(
      new Date(event?.end?.dateTime.split("+")[0]) -
        new Date(event?.start?.dateTime.split("+")[0])
    );

    let end = timeDifference / (1000 * 60 * 60);

    end = end * 12;

    return { start, end };
  };

  function classNames(...classes) {
    return classes.filter(Boolean).join(" ");
  }

  const handleDay = (action) => {
    if (action === "prev") {
      setSlide(true);
      // console.log(new Date(currentDate.setDate(currentDate.getDate() - 1)))
      setCurrentDate(new Date(currentDate.setDate(currentDate.getDate() - 1)));
    }
    if (action === "next") {
      setSlide(true);
      setCurrentDate(new Date(currentDate.setDate(currentDate.getDate() + 1)));
      // console.log(selected)
    }

    if (action === "today") {
      // console.log(new Date())
      setCurrentDate(new Date());
      setSlide(true);
    }
  };

  const googleEvents = async () => {
    try {
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let data = {
        startDate: moment(currentDate).startOf("day"),
        endDate: moment(currentDate).endOf("day"),
      };

      let requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: JSON.stringify(data),
      };
      const response = await fetch(`/api/calendar`, requestOptions);

      const result = await response.json();
      let tempEvents = [];
      if (!result.error) {
        result.data &&
          result.data.length > 0 &&
          result.data.map((x) => {
            if (
              formatDateToYYYYMMDD(new Date(currentDate)) ==
              formatDateToYYYYMMDD(new Date(x.start.dateTime.split("+")[0]))
            ) {
              // console.log(x);
              tempEvents.push(x);
            }
          });
        setAllEvents(result.data);
      }
      if (!result) {
        window.location.reload();
      }
    } catch (error) {
      console.log(error, "error");
    }
  };

  useEffect(() => {
    googleEvents();
  }, [
    currentDate,
    setCurrentDate,
    successNotification,
    setSuccessNotification,
  ]);

  return (
    <div className="flex h-full flex-col">
      {successNotification && <SuccessNotification message={message} />}

      {errorNotification && <ErrorNotification message={message} />}
      <header
        className="flex flex-none items-center flex-col md:flex-row md:justify-between border-b border-gray-200 px-6 py-4"
        style={{
          position: "sticky",
          zIndex: "3",
          top: "10%",
          background: "#fff",
        }}
      >
        <div className="order-1 text-center">
          <h1 className="text-base font-semibold leading-6 text-gray-900">
            <time dateTime="2022-01-22" className="sm:hidden">
              {`${currentDate.getDate()} ${
                months[currentDate.getMonth()]
              } ${currentDate.getFullYear()}`}
            </time>
            <time dateTime="2022-01-22" className="hidden sm:inline">
              {`${currentDate.getDate()} ${
                months[currentDate.getMonth()]
              } ${currentDate.getFullYear()}`}
            </time>
          </h1>
          <p className="md:mt-1 md:text-sm md:text-gray-500 max-sm:hidden">
            {daysOfWeek[currentDate.getDay()]}
          </p>
        </div>

        <ul className=" order-2 flex flex-wrap mt-2 justify-center">
          <li className=" max-sm:w-[33%]">
            <span className="inline-flex items-center gap-x-1.5 rounded-full ml-2 bg-red-100 px-1.5 py-0.5 text-xs font-medium text-red-700">
              <svg
                className="h-1.5 w-1.5 fill-red-500"
                viewBox="0 0 6 6"
                aria-hidden="true"
              >
                <circle cx={3} cy={3} r={3} />
              </svg>
              Breaks
            </span>
          </li>
          <li className=" max-sm:w-[33%]">
            <span className="inline-flex items-center gap-x-1.5 ml-2 rounded-full bg-gray-100 px-1.5 py-0.5 text-xs font-medium text-gray-600">
              <svg
                className="h-1.5 w-1.5 fill-gray-500"
                viewBox="0 0 6 6"
                aria-hidden="true"
              >
                <circle cx={3} cy={3} r={3} />
              </svg>
              Courses
            </span>
          </li>
          <li className=" max-sm:w-[33%]">
            <span className="inline-flex items-center gap-x-1.5 ml-2 rounded-full bg-green-100 px-1.5 py-0.5 text-xs font-medium text-green-700">
              <svg
                className="h-1.5 w-1.5 fill-green-500"
                viewBox="0 0 6 6"
                aria-hidden="true"
              >
                <circle cx={3} cy={3} r={3} />
              </svg>
              Bookings
            </span>
          </li>
          <li className=" max-sm:w-[33%]">
            <span className="inline-flex items-center gap-x-1.5 ml-2 rounded-full bg-amber-100 px-1.5 py-0.5 text-xs font-medium text-yellow-800">
              <svg
                className="h-1.5 w-1.5 fill-amber-500"
                viewBox="0 0 6 6"
                aria-hidden="true"
              >
                <circle cx={3} cy={3} r={3} />
              </svg>
              Sessions
            </span>
          </li>
          <li className=" max-sm:w-[33%]">
            <span className="inline-flex items-center gap-x-1.5 ml-2 rounded-full bg-blue-100 px-1.5 py-0.5 text-xs font-medium text-blue-700">
              <svg
                className="h-1.5 w-1.5 fill-blue-500"
                viewBox="0 0 6 6"
                aria-hidden="true"
              >
                <circle cx={3} cy={3} r={3} />
              </svg>
              Other Events
            </span>
          </li>
        </ul>

        <TaskModal
          open={open}
          setOpen={setOpen}
          selectedDay={selectedDay}
          setSelectedDay={setSelectedDay}
          taskName={taskName}
          setTaskName={setTaskName}
          taskStartDate={taskStartDate}
          setTaskStartDate={setTaskStartDate}
          startTime={startTime}
          setStartTime={setStartTime}
          endTime={endTime}
          setEndTime={setEndTime}
          formatDateToYYYYMMDD={formatDateToYYYYMMDD}
          isEnd={isEnd}
          errorNotification={errorNotification}
          setIsEnd={setIsEnd}
          successNotification={successNotification}
          setSuccessNotification={setSuccessNotification}
          setErrorNotification={setErrorNotification}
          setMessage={setMessage}
          message={message}
          loading={loading}
          setLoading={setLoading}
        />

        <DeleteModal
          openDeleteModal={openDeleteModal}
          setOpenDeleteModal={setOpenDeleteModal}
          googleEventId={googleEventId}
          setGoogleEventId={setGoogleEventId}
          successNotification={successNotification}
          setSuccessNotification={setSuccessNotification}
          errorNotification={errorNotification}
          setErrorNotification={setErrorNotification}
          setMessage={setMessage}
          loading={loading}
          setLoading={setLoading}
        />

        <div className="flex items-center order-2 md:order-3 justify-center mt-4">
          <div className="relative flex items-center rounded-md bg-white shadow-sm md:items-stretch">
            <button
              type="button"
              className="flex h-9 w-10 items-center justify-center rounded-l-md border border-gray-300 pr-1 text-gray-400 hover:text-gray-500 focus:relative md:w-9 md:pr-0 md:hover:bg-gray-50"
              onClick={() => handleDay("prev")}
            >
              <span className="sr-only">Previous day</span>
              <ChevronLeftIcon className="h-5 w-5" aria-hidden="true" />
            </button>
            <button
              type="button"
              className="hidden border-y border-gray-300 px-3.5 text-sm font-semibold text-gray-900 hover:bg-gray-50 focus:relative md:block"
              onClick={() => handleDay("today")}
            >
              Today
            </button>
            <span className="relative -mx-px h-5 w-px bg-gray-300 md:hidden" />
            <button
              type="button"
              className="flex h-9 w-12 items-center justify-center rounded-r-md border border-r border-gray-300 pl-1 text-gray-400 hover:text-gray-500 focus:relative md:w-9 md:pl-0 md:hover:bg-gray-50"
              onClick={() => handleDay("next")}
            >
              <span className="sr-only">Next day</span>
              <ChevronRightIcon className="h-5 w-5" aria-hidden="true" />
            </button>
          </div>
          <div className="ml-5">
            <button
              type="button"
              onClick={() => setOpen(!open)}
              className="inline-flex items-center gap-x-1.5 rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
            >
              Add Event
            </button>
          </div>
        </div>
      </header>
      <div className="isolate flex flex-auto overflow-hidden bg-white">
        <div ref={container} className="flex flex-auto flex-col overflow-auto">
          <div
            ref={containerNav}
            className=" sticky top-0 z-10 md:hidden"
          ></div>
          <div className="flex w-full flex-auto">
            <div className="w-14 flex-none bg-white ring-1 ring-gray-100" />
            <div className="grid flex-auto grid-cols-1 grid-rows-1 md:max-w-[60%] max-sm:w-[100%]">
              {/* Horizontal lines */}
              <div
                className="col-start-1 col-end-2 row-start-1 grid divide-y divide-gray-100"
                style={{ gridTemplateRows: "repeat(48, minmax(3.5rem, 1fr))" }}
              >
                <div ref={containerOffset} className="row-end-1 h-7"></div>
                <div>
                  <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                    12AM
                  </div>
                </div>
                <div />
                <div>
                  <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                    1AM
                  </div>
                </div>
                <div />
                <div>
                  <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                    2AM
                  </div>
                </div>
                <div />
                <div>
                  <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                    3AM
                  </div>
                </div>
                <div />
                <div>
                  <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                    4AM
                  </div>
                </div>
                <div />
                <div>
                  <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                    5AM
                  </div>
                </div>
                <div />
                <div>
                  <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                    6AM
                  </div>
                </div>
                <div />
                <div>
                  <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                    7AM
                  </div>
                </div>
                <div />
                <div>
                  <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                    8AM
                  </div>
                </div>
                <div />
                <div>
                  <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                    9AM
                  </div>
                </div>
                <div />
                <div>
                  <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                    10AM
                  </div>
                </div>
                <div />
                <div>
                  <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                    11AM
                  </div>
                </div>
                <div />
                <div>
                  <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                    12PM
                  </div>
                </div>
                <div />
                <div>
                  <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                    1PM
                  </div>
                </div>
                <div />
                <div>
                  <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                    2PM
                  </div>
                </div>
                <div />
                <div>
                  <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                    3PM
                  </div>
                </div>
                <div />
                <div>
                  <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                    4PM
                  </div>
                </div>
                <div />
                <div>
                  <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                    5PM
                  </div>
                </div>
                <div />
                <div>
                  <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                    6PM
                  </div>
                </div>
                <div />
                <div>
                  <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                    7PM
                  </div>
                </div>
                <div />
                <div>
                  <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                    8PM
                  </div>
                </div>
                <div />
                <div>
                  <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                    9PM
                  </div>
                </div>
                <div />
                <div>
                  <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                    10PM
                  </div>
                </div>
                <div />
                <div>
                  <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                    11PM
                  </div>
                </div>
                <div />
              </div>

              {/* Events */}
              <ol
                className="col-start-1 col-end-2 row-start-1 grid ml-3 overflow-x-auto max-md:gap-x-[10px] md:gap-x-[10px] max-md:max-w-[85%] lg:gap-x-[10px]  md:max-w-[85%] lg:max-w-[75%] xl:max-w-[87%] 2xl:max-w-[100%] max-sm:max-w-[100%] max-sm:w-[100%] max-sm:gap-x-[30px]"
                style={{
                  gridTemplateRows: "1.75rem repeat(288, minmax(0, 1fr)) auto",
                }}
              >
                {allEvents?.map((x, i) => {
                  let type = "";
                  let id = "";

                  try {
                    const parsedDescription = JSON.parse(x?.description);
                    type = parsedDescription.type;
                    id = parsedDescription.id;
                  } catch (error) {
                    console.error("Error parsing description:", error);
                  }
                  return (
                    <li
                      key={`key${i}`}
                      className="relative mt-px flex"
                      style={{
                        gridRow: `${calculateEvents(x).start} / span ${
                          calculateEvents(x).end
                        }`,
                      }}
                    >
                      {console.log(
                        `${calculateEvents(x).start} / span ${
                          calculateEvents(x).end
                        }`,
                        "events",
                        x?.summary
                      )}
                      <Link
                        onClick={() => {
                          if (x?.colorId === "1") {
                            setGoogleEventId(x.id);
                            setOpenDeleteModal(true);
                          } else {
                            setOpenDeleteModal(false);
                          }
                        }}
                        href={
                          x?.colorId && x?.description
                            ? type == "course"
                              ? `/course/create?coach=${id}`
                              : type == "bookings"
                              ? `/bookings/${id}`
                              : ""
                            : ""
                        }
                        className={`group inset-1 flex flex-col shrink-0 overflow-y-auto rounded-lg ${
                          x?.colorId
                            ? (x?.colorId === "1" &&
                                "bg-red-300 p-2 text-xs leading-5 hover:bg-red-400") ||
                              (x?.colorId === "2" &&
                                "bg-gray-300 p-2 text-xs leading-5 hover:bg-gray-500") ||
                              (x?.colorId === "3" &&
                                "bg-green-300 p-2 text-xs leading-5 hover:bg-green-500") ||
                              (x?.colorId === "4" &&
                                "bg-amber-300 p-2 text-xs leading-5 hover:bg-amber-500") ||
                              (x?.colorId === "5" &&
                                "bg-blue-50 p-2 text-xs leading-5 hover:bg-blue-100")
                            : "bg-blue-50 p-2 text-xs leading-5 hover:bg-blue-100"
                        }`}
                      >
                        <p
                          className={`order-1 font-semibold break-words w-[110px] block ${
                            x?.colorId
                              ? (x?.colorId === "1" && "text-white") ||
                                (x?.colorId === "2" && "text-white") ||
                                (x?.colorId === "3" && "text-white") ||
                                (x?.colorId === "4" && "text-white") ||
                                (x?.colorId === "5" && "text-blue-700")
                              : "text-blue-700"
                          }`}
                          style={{ zIndex: i === 0 ? 1 : i + 1 }}
                        >
                          {x?.summary}
                        </p>
                        <p
                          className={`${
                            x?.colorId
                              ? (x?.colorId === "1" &&
                                  "text-white group-hover:text-white") ||
                                (x?.colorId === "2" &&
                                  "text-white group-hover:text-white") ||
                                (x?.colorId === "3" &&
                                  "text-white group-hover:text-white") ||
                                (x?.colorId === "4" &&
                                  "text-white group-hover:text-white") ||
                                (x?.colorId === "5" &&
                                  "text-blue-700 group-hover:text-blue-700")
                              : "text-blue-700 group-hover:text-blue-700"
                          }`}
                        >
                          <time
                            dateTime={x?.start?.dateTime?.split("+")[0]}
                          >{`${new Date(
                            x?.start?.dateTime?.split("+")[0]
                          ).toLocaleString("en-IN", {
                            hour: "numeric",
                            minute: "numeric",
                            hour12: true,
                          })} - ${new Date(
                            x?.end?.dateTime?.split("+")[0]
                          ).toLocaleString("en-IN", {
                            hour: "numeric",
                            minute: "numeric",
                            hour12: true,
                          })}`}</time>
                        </p>
                      </Link>
                    </li>
                  );
                })}
              </ol>
            </div>
          </div>
        </div>
        <div
          className="hidden w-1/2 max-w-md flex-none border-gray-100 px-8 py-10 md:block"
          style={{
            position: "fixed",
            top: "23%",
            zIndex: "3",
            height: "100%",
            right: "0",
            width: "40%",
            background: "#fff",
          }}
        >
          <Calendar
            onChange={(value) => {
              setCurrentDate(value);
            }}
            prev2Label={null}
            next2Label={null}
            value={currentDate}
            onActiveStartDateChange={() => setSlide(false)}
            activeStartDate={slide ? currentDate : false}
            // defaultValue={new Date()}
          />
        </div>
      </div>
    </div>
  );
}

function TaskModal({
  open,
  setOpen,
  selectedDay,
  setSelectedDay,
  taskStartDate,
  setTaskStartDate,
  taskName,
  setTaskName,
  startTime,
  setStartTime,
  endTime,
  setEndTime,
  formatDateToYYYYMMDD,
  isEnd,
  setIsEnd,
  setSuccessNotification,
  setErrorNotification,
  setMessage,
  message,
  loading,
  setLoading,
  errorNotification,
}) {
  const daysOfWeek = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];

  const [endDate, setEndDate] = useState("");

  const [error, setError] = useState(false);
  const [startTimeError, setStartTimeError] = useState(false);
  const [endTimeError, setEndTimeError] = useState(false);

  function getLastDateOfCurrentYear() {
    var today = new Date(); // Get current date
    var currentYear = today.getFullYear(); // Get current year
    var lastDate = new Date(currentYear, 11, 31); // Set to December 31st of the current year
    return formatDateToYYYYMMDD(lastDate);
  }

  function daysDifference(startDate, endDate, daysArray) {
    const daysOfWeek = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const start = new Date(startDate);
    const end = new Date(endDate);

    let count = 0;
    let currentDate = new Date(start);

    while (currentDate <= end) {
      if (daysArray.includes(daysOfWeek[currentDate.getDay()])) {
        count++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return count;
  }

  const getTime = (time) => {
    const tempdate = new Date(time);

    const hours = tempdate.getHours(); // Get the hour component (0-23)
    const minutes = tempdate.getMinutes(); // Get the minute component (0-59)

    // console.log(hours, minutes);

    // Format the time as needed (e.g., to a string with leading zeros)
    const formattedTime = `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}`;

    return formattedTime;
  };

  const saveTask = async (e) => {
    try {
      e.preventDefault();

      if (!startTime || startTime == "") {
        setStartTimeError(true);
        setMessage("Please select atleast one day");
        return;
      }
      if (!endTime || endTime == "") {
        setEndTimeError(true);
        setMessage("Please select atleast one day");
        return;
      }

      if (selectedDay.length == 0) {
        setError(true);
        setMessage("Please select atleast one day");
        return;
      }
      setLoading(true);
      const datetimeString = `${taskStartDate}T${getTime(startTime)}:00`;
      const taskEndDate = `${
        endDate ? endDate : getLastDateOfCurrentYear()
      }T${getTime(endTime)}:00`;
      const startDateTime = moment.tz(datetimeString, "Asia/Kolkata").format();
      const endDateTime = moment.tz(taskEndDate, "Asia/Kolkata").format();
      let googleEndDate = `${taskStartDate}T${getTime(endTime)}:00`;
      googleEndDate = moment.tz(googleEndDate, "Asia/Kolkata").format();

      const isDayAvailable = await daysDifference(
        taskStartDate,
        taskEndDate,
        selectedDay
      );

      if (isDayAvailable == 0) {
        setLoading(false);
        setSuccessNotification(false);
        setMessage("Selected days are not in between the selected dates.");
        setErrorNotification(true);
        setTimeout(() => {
          setSuccessNotification(false);
          setErrorNotification(false);
        }, 3000);
        return;
      }

      const obj = {
        summary: taskName,
        startDateTime: startDateTime,
        endDate: endDateTime,
        daysCount: daysDifference(startDateTime, endDateTime, selectedDay),
        endDateTime: googleEndDate,
        days: selectedDay,
        colorId: "1",
      };

      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: JSON.stringify(obj),
      };
      const response = await fetch(`/api/calendar/add_event`, requestOptions);

      const result = await response.json();

      if (!result.error) {
        setLoading(false);
        setOpen(!open);
        setSuccessNotification(true);
        setMessage("Task added successfully");
        setErrorNotification(false);
        setSelectedDay([]);
        setStartTime("");
        setEndTime("");
        setIsEnd(false);
        setTimeout(() => {
          setSuccessNotification(false);
          setErrorNotification(false);
        }, 3000);
      } else {
        setLoading(false);
        setSuccessNotification(false);
        setMessage("Something went wrong. Please try again!!");
        setErrorNotification(true);
        setTimeout(() => {
          setSuccessNotification(false);
          setErrorNotification(false);
        }, 3000);
      }

      // console.log(result, "result");
    } catch (error) {
      setLoading(false);
      console.log(error);
      setErrorNotification(true);
      setMessage("Something went wrong. Please try again!!");
      setSuccessNotification(false);
      setTimeout(() => {
        setSuccessNotification(false);
        setErrorNotification(false);
      }, 3000);
    }
  };

  const getCurrentTime = () => {
    const currentDate = new Date();
    const currentHours = currentDate.getHours();
    const currentMinutes = currentDate.getMinutes();
    return new Date(0, 0, 0, currentHours, currentMinutes);
  };

  const currentTime = getCurrentTime();

  const date = new Date(taskStartDate);
  const today = new Date();

  const includesToday =
    date.getDate() <= today.getDate() &&
    date.getMonth() <= today.getMonth() &&
    date.getFullYear() <= today.getFullYear();

  const filterPassedTime = (time) => {
    if (!startTime) return true;
    const selectedTime = new Date(time);
    const minAllowedTime = getMinTime();
    return selectedTime >= minAllowedTime;
  };

  const minTime = includesToday ? currentTime : undefined;

  const getMinTime = () => {
    if (!startTime) return null;

    const timeFromObj = new Date(startTime);
    const tenMinutesLater = new Date(timeFromObj.getTime() + 10 * 60000); // 10 minutes in milliseconds

    return tenMinutesLater;
  };

  return (
    <>
      <Transition.Root show={open} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={() => false}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </Transition.Child>
          <div className="fixed inset-0 z-50 w-screen overflow-y-auto">
            <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                enterTo="opacity-100 translate-y-0 sm:scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              >
                <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-sm sm:p-6">
                  <form onSubmit={(e) => saveTask(e)}>
                    <button
                      type="button"
                      onClick={() => {
                        setOpen(!open);
                      }}
                      className="bg-white float-right top-0 rounded-md p-2 inline-flex items-center justify-center text-gray-400 hover:text-white hover:bg-white focus:outline-none"
                    >
                      <span className="sr-only">Close menu</span>
                      <svg
                        className="h-6 w-6 text-red-500"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </button>
                    <h4 className="flex justify-center bold">Add Event</h4>
                    <div className="mt-5">
                      <div>
                        <label
                          htmlFor="task_name"
                          className="block mb-2 text-[16px]font-medium text-gray-900 dark:text-black capitalize required"
                        >
                          Enter Task Name
                        </label>
                        <input
                          type="text"
                          id="task_name"
                          className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5  dark:border-gray-600 dark:placeholder-gray-400 dark:text-black dark:focus:ring-blue-500 dark:focus:border-blue-500"
                          placeholder="Task Name"
                          onChange={(e) => setTaskName(e.target.value)}
                          required
                          maxLength={50}
                        />
                      </div>

                      <div className="mt-3">
                        <label
                          htmlFor="start_date"
                          className="block mb-2 text-[16px]font-medium text-gray-900 dark:text-black capitalize required"
                        >
                          Start Date:
                        </label>
                        <input
                          type="date"
                          min={formatDateToYYYYMMDD(new Date())}
                          id="start_date"
                          onChange={(e) => setTaskStartDate(e.target.value)}
                          required
                          className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5  dark:border-gray-600 dark:placeholder-gray-400 dark:text-black dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        />
                      </div>

                      <div className="mt-3">
                        <div>
                          <label
                            htmlFor="start_date"
                            className="block mb-2 text-[16px]font-medium text-gray-900 dark:text-black capitalize required"
                          >
                            From:
                          </label>
                          <DatePicker
                            selected={startTime}
                            onKeyDown={(e) => e.preventDefault()}
                            className={
                              startTimeError
                                ? "my-custom-datepicker dateError"
                                : "my-custom-datepicker"
                            }
                            onChange={(value) => {
                              if (value == "") {
                                setStartTimeError(true);
                              } else {
                                setStartTimeError(false);
                              }
                              setStartTime(value);
                              setEndTime("");
                            }}
                            showTimeSelect
                            showTimeSelectOnly
                            timeIntervals={10}
                            timeCaption="Time"
                            dateFormat="h:mm aa"
                            popperPlacement="left-start"
                            placeholderText="Select time"
                            disabled={taskStartDate == ""}
                            minTime={minTime || new Date(0, 0, 0, 0, 0)}
                            maxTime={new Date(0, 0, 0, 23, 59)}
                            // excludeTimes={excludedSlots || []}
                          />
                          <br />
                          {startTimeError && (
                            <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                              Select the time
                            </span>
                          )}
                        </div>
                        <div>
                          <label
                            htmlFor="start_date"
                            className="block  text-[16px]font-medium text-gray-900 dark:text-black capitalize required"
                          >
                            To:
                          </label>
                          <DatePicker
                            selected={endTime}
                            onKeyDown={(e) => e.preventDefault()}
                            onChange={async (value) => {
                              // console.log(value, "timetotoo");
                              setEndTime(value);
                              if (value == "") {
                                setEndTimeError(true);
                              } else {
                                setEndTimeError(false);
                              }
                            }}
                            showTimeSelect
                            showTimeSelectOnly
                            timeIntervals={10}
                            timeCaption="Time"
                            dateFormat="h:mm aa"
                            className={
                              endTimeError
                                ? "my-custom-datepicker dateError"
                                : "my-custom-datepicker"
                            }
                            popperPlacement="left-start"
                            placeholderText="Select time"
                            disabled={startTime === ""}
                            minTime={getMinTime()}
                            filterTime={filterPassedTime}
                            maxTime={new Date(0, 0, 0, 23, 59)}
                            // excludeTimes={excludedSlots || []}
                          />
                          <br />
                          {endTimeError && (
                            <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                              Select the time
                            </span>
                          )}
                        </div>
                      </div>

                      <div className="mt-3">
                        <label
                          htmlFor="end_date"
                          className="block mb-2 text-[16px]font-medium text-gray-900 dark:text-black capitalize required"
                        >
                          End Date:
                        </label>
                        <ul className="items-center w-full text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg sm:flex dark:border-gray-600 dark:text-white">
                          <li className="w-full border-b border-gray-200 sm:border-b-0 sm:border-r dark:border-gray-600">
                            <div className="flex items-center ps-3">
                              <input
                                id="horizontal-list-radio-license"
                                type="radio"
                                required
                                value=""
                                onChange={(e) =>
                                  e.target.checked
                                    ? setIsEnd(false)
                                    : setIsEnd(true)
                                }
                                name="list-radio"
                                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300  dark:bg-gray-600 dark:border-gray-500"
                              />
                              <label
                                htmlFor="horizontal-list-radio-license"
                                className="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-black-300"
                              >
                                Never
                              </label>
                            </div>
                          </li>

                          <li className="w-full border-b border-gray-200 sm:border-b-0 sm:border-r dark:border-gray-600">
                            <div className="flex items-center ps-3">
                              <input
                                id="horizontal-list-radio-license"
                                type="radio"
                                required
                                value=""
                                onChange={(e) =>
                                  e.target.checked
                                    ? setIsEnd(true)
                                    : setIsEnd(false)
                                }
                                name="list-radio"
                                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300  dark:bg-gray-600 dark:border-gray-500"
                              />
                              <label
                                htmlFor="horizontal-list-radio-license"
                                className="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-black-300"
                              >
                                On
                              </label>
                            </div>
                          </li>
                        </ul>
                      </div>

                      {isEnd && (
                        <div className="mt-3">
                          <label
                            htmlFor="start_date"
                            className="block mb-2 text-[16px]font-medium text-gray-900 dark:text-black capitalize required"
                          >
                            End Date:
                          </label>
                          <input
                            type="date"
                            min={formatDateToYYYYMMDD(taskStartDate)}
                            id="start_date"
                            onChange={(e) => setEndDate(e.target.value)}
                            required
                            className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5  dark:border-gray-600 dark:placeholder-gray-400 dark:text-black dark:focus:ring-blue-500 dark:focus:border-blue-500"
                          />
                        </div>
                      )}

                      <div className="mt-4 ">
                        <label className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize">
                          Select the Days
                        </label>
                        <div className="grid grid-cols-7 text-center text-xs leading-6 text-gray-500">
                          {daysOfWeek.map((day, dayIdx) => (
                            <button
                              key={dayIdx}
                              type="button"
                              onClick={() => {
                                setError(false);
                                setSelectedDay(
                                  selectedDay.includes(day)
                                    ? selectedDay.filter(
                                        (selected) => selected !== day
                                      )
                                    : [...selectedDay, day]
                                );
                              }}
                              className={`${
                                selectedDay.includes(day)
                                  ? "bg-blue-500 text-white"
                                  : ""
                              } p-2 rounded-full`}
                            >
                              {day}
                            </button>
                          ))}
                        </div>
                        {error && (
                          <p className="text-red-500">
                            Please select atleast one day
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="mt-3 sm:mt-6">
                      {loading ? (
                        <button
                          disabled
                          type="button"
                          className="text-white px-3 py-2.5 bg-indigo-700 hover:bg-blue-800 w-full font-medium rounded text-sm text-center mr-2 dark:bg-indigo-600 dark:hover:bg-indigo-700  inline-flex items-center"
                        >
                          <svg
                            aria-hidden="true"
                            role="status"
                            class="inline mr-3 w-4 h-4 text-white animate-spin"
                            viewBox="0 0 100 101"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                              fill="#E5E7EB"
                            ></path>
                            <path
                              d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                              fill="currentColor"
                            ></path>
                          </svg>
                          Loading...
                        </button>
                      ) : (
                        <button
                          type="submit"
                          className="inline-flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                        >
                          Add
                        </button>
                      )}
                      {errorNotification && (
                        <div className="text-red-500">{message}</div>
                      )}
                    </div>
                  </form>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition.Root>
    </>
  );
}

function DeleteModal({
  openDeleteModal,
  setOpenDeleteModal,
  googleEventId,
  setErrorNotification,
  setMessage,
  setSuccessNotification,
  loading,
  setLoading,
}) {
  const deleteTask = async () => {
    try {
      setLoading(true);
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: JSON.stringify({ eventId: googleEventId }),
      };
      const response = await fetch(
        `/api/calendar/delete_event`,
        requestOptions
      );

      const result = await response.json();

      // console.log(result, "result here");

      if (result == "200") {
        setOpenDeleteModal(!openDeleteModal);
        setLoading(false);
        setMessage("Task deleted successfully");
        setSuccessNotification(true);
        setErrorNotification(false);
        setTimeout(() => {
          setSuccessNotification(false);
          setErrorNotification(false);
        }, 3000);
      } else {
        setOpenDeleteModal(!openDeleteModal);
        setSuccessNotification(false);
        setLoading(false);
        setMessage("Something went wrong. Please try again!!");
        setErrorNotification(true);
        setTimeout(() => {
          setSuccessNotification(false);
          setErrorNotification(false);
        }, 3000);
      }

      // console.log(result, "result");
    } catch (error) {
      // console.log("enter3");
      console.log(error);
      setOpenDeleteModal(!openDeleteModal);
      setLoading(false);
      setErrorNotification(true);
      setMessage("Something went wrong. Please try again!!");
      setSuccessNotification(false);
      setTimeout(() => {
        setSuccessNotification(false);
        setErrorNotification(false);
      }, 3000);
    }
  };

  return (
    <Transition.Root show={openDeleteModal} as={Fragment}>
      <Dialog
        as="div"
        className="relative z-10"
        onClose={() => {
          setOpenDeleteModal(false);
        }}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-sm sm:p-6">
                <div>
                  <div className="mt-3 text-center sm:mt-5">
                    <Dialog.Title
                      as="h3"
                      className="text-base font-semibold leading-6 text-gray-900"
                    >
                      {"Delete Task ?"}
                    </Dialog.Title>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Do you want to delete the task ? It will also delete
                        from your google account.
                      </p>
                    </div>
                  </div>
                </div>
                <div className="mt-5 sm:mt-6 flex justify-around">
                  <button
                    type="button"
                    className="inline-flex justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                    onClick={() => {
                      setOpenDeleteModal(false);
                      setLoading(false);
                    }}
                  >
                    Cancel
                  </button>
                  {loading ? (
                    <button
                      disabled
                      type="button"
                      className="text-white px-3 py-2.5 bg-red-500 hover:bg-red-600   font-medium rounded text-sm text-center mr-2 dark:bg-red-500 dark:hover:bg-red-600  inline-flex items-center"
                    >
                      <svg
                        aria-hidden="true"
                        role="status"
                        class="inline mr-3 w-4 h-4 text-white animate-spin"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                          fill="#E5E7EB"
                        ></path>
                        <path
                          d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                          fill="currentColor"
                        ></path>
                      </svg>
                      Loading...
                    </button>
                  ) : (
                    <button
                      type="button"
                      className="inline-flex justify-center rounded-md bg-red-500 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600"
                      onClick={() => deleteTask()}
                    >
                      Delete
                    </button>
                  )}
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
