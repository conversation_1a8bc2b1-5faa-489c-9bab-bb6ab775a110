import { NextResponse } from "next/server";

export async function GET(req) {
  try {
    // console.log("enter");

    const resp = NextResponse.json({
      message: "Logout Success",
      success: true,
    });

    resp.cookies.set("coach-token", "", { httpOnly: true, maxAge: -1 });
    resp.cookies.set("id", "", { httpOnly: true, maxAge: 86400 });
    return resp;
  } catch (error) {
    console.error("error", error);
    return new NextResponse(JSON.stringify({ error: error }));
  }
}
