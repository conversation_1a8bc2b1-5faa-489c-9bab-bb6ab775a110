import axios from "axios";
import { NextResponse } from "next/server";
import { verifyToken } from "@/helpers/getDataFromToken";
import API from "@/components/API";

export async function GET(req) {
  try {
    const token = req.cookies.get("coach-token")?.value || "";

    const payload = await verifyToken(token);

    const response = await axios.get(`${API}/api/calendar/google`, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    // console.log(response.data, "resp")

    return new NextResponse(JSON.stringify(response.data));
  } catch (error) {
    console.error("error 23");
    return new NextResponse(JSON.stringify({ error: error }));
  }
}

export async function POST(req) {
  try {
    let reqBody = await req.json();

    const token = req.cookies.get("coach-token")?.value || "";

    const payload = await verifyToken(token);

    const response = await axios.post(
      `${API}/api/calendar/eventList`,
      reqBody,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );
    // console.log(response.data, "resp")

    return new NextResponse(JSON.stringify(response.data));
  } catch (error) {
    return new NextResponse(JSON.stringify({ error: error }));
  }
}
