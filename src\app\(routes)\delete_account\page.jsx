"use client";

import React, { useState } from "react";
import <PERSON> from "next/link";
import { useRouter } from "next/navigation";
import SuccessNotification from "@/components/Notification/SuccessNotification";
import ErrorNotification from "@/components/Notification/ErrorNotification";

const ConfirmAccountDeletion = () => {
  const router = useRouter();

  const [reason, setReason] = useState("");
  const [successNotification, setSuccessNotification] = useState(false);
  const [errorNotification, setErrorNotification] = useState(false);
  const [message, setMessage] = useState("");
  const [loading, setLoading] = useState(false);

  const handleLogout = async () => {
    try {
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");
      let requestOptions = {
        method: "GET",
        headers: myHeaders,
        // body: JSON.stringify(values),
      };
      const response = await fetch(`/api/logout`, requestOptions);
      const result = await response.json();

      router.push(`/login`);
    } catch (error) {
      console.log(error);
    }
  };

  const handleDeleteAccount = async () => {
    try {
      setLoading(true);
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let requestOptions = {
        method: "Post",
        headers: myHeaders,
      };

      const response = await fetch(`/api/delete_account`, requestOptions);

      const result = await response.json();
      setLoading(false);
      if (!result.error) {
        setSuccessNotification(true);
        setMessage("Account deleted successfully");
        handleLogout();
        setTimeout(() => {
          setSuccessNotification(false);
        }, 4000);
      } else {
        setErrorNotification(true);
        setMessage("Coach with courses can't be deleted");
        setTimeout(() => {
          setErrorNotification(false);
        }, 4000);
      }
    } catch (error) {
      setLoading(false);
      setOpen(!open);
      setErrorNotification(true);
      setMessage("Account can not be deleted at the moment");
      setTimeout(() => {
        setErrorNotification(false);
      }, 4000);
      console.log(error, "error");
    }
  };

  const handleConfirm = () => {
    if (reason) {
      // Add your account deletion logic here
      handleDeleteAccount();
    } else {
      alert("Please enter your reason");
    }
  };

  return (
    <>
      {successNotification && <SuccessNotification message={message} />}

      {errorNotification && <ErrorNotification message={message} />}

      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <div className="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
          <h2 className="text-xl font-semibold text-center mb-4">
            Confirm Account Deletion
          </h2>
          <p className="text-gray-800 mb-4">
            Are you sure? Your profile and related account information will be
            deleted from our site.
          </p>

          <p className="text-black-600 font-bold mb-4">
            Note:- Coach who has not created any Course can delete their
            account.
          </p>
          <p className="text-yellow-600 font-bold mb-4">
            To confirm deletion, please enter your reason below:
          </p>
          <textarea
            className="w-full p-2 border border-gray-300 rounded-lg mb-4"
            placeholder="Please enter your reason"
            value={reason}
            onChange={(e) => setReason(e.target.value)}
          />
          <div className="flex justify-end space-x-4">
            {loading ? (
              <button
                disabled
                type="button"
                className="text-white px-3 py-2.5 bg-red-500 hover:bg-red-600   font-medium rounded text-sm text-center mr-2 dark:bg-red-500 dark:hover:bg-red-600  inline-flex items-center"
              >
                <svg
                  aria-hidden="true"
                  role="status"
                  class="inline mr-3 w-4 h-4 text-white animate-spin"
                  viewBox="0 0 100 101"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                    fill="#E5E7EB"
                  ></path>
                  <path
                    d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                    fill="currentColor"
                  ></path>
                </svg>
                Loading...
              </button>
            ) : (
              <button
                type="button"
                className="inline-flex justify-center rounded-md bg-red-500 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600"
                onClick={() => handleConfirm()}
              >
                Delete
              </button>
            )}
            <Link
              href="/"
              className="block px-3 py-1 text-sm leading-6 text-gray-900 hover:bg-gray-100"
            >
              Cancel
            </Link>
          </div>
        </div>
      </div>
    </>
  );
};

export default ConfirmAccountDeletion;
