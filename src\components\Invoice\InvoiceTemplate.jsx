import React from "react";
// import { useParams } from "next/navigation";
import { useEffect, useState, useRef, Fragment } from "react";
import { Transition, Dialog } from "@headlessui/react";
import { useReactToPrint } from "react-to-print";
import { convertDateIntoIndianFormat } from "@/helpers/dateHelpers";
import { numberToWords } from "@/helpers/convertIntoWords";

const InvoiceTemplate = ({ open, setOpen, singleReport, singleReportIndex }) => {
  const invoiceRef = useRef();
  const handlePrint = useReactToPrint({
    content: () => invoiceRef.current,
  });
  const cancelButtonRef = useRef(null);

  const [coach, setCoach] = useState();
  const [loading, setLoading] = useState(false);

  const getCoachDetails = async () => {
    try {
      setLoading(true);
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let requestOptions = {
        method: "GET",
        headers: myHeaders,
      };
      const response = await fetch(`/api/coach_profile`, requestOptions);
      const result = await response.json();
      setCoach(result);
      setLoading(false);
      console.log(result);
    } catch (error) {
      setLoading(false);
      console.log(error, "error");
    }
  };

  useEffect(() => {
    getCoachDetails();
  }, []);
  return (
    <div>
      <Transition.Root show={open} as={Fragment}>
        <Dialog
          as="div"
          className="relative z- flex items-center justify-center h-screen"
          initialFocus={cancelButtonRef}
          onClose={() => setOpen(!open)}
        >
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 z-50 w-screen overflow-y-auto">
            <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                enterTo="opacity-100 translate-y-0 sm:scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              >
                <Dialog.Panel className="relative transform rounded-lg px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:mb-8 sm:w-full max-w-[80%] mt-[5rem] sm:p-6">
                  <div className=" bg-white p-10 shadow-md">
                    <div>
                      <button
                        onClick={handlePrint}
                        className="flex text-sm font-medium text-indigo-600 hover:text-indigo-500 sm:block p-[1rem] fixed right-5"
                      >
                        Print invoice
                        <span aria-hidden="true"> &rarr;</span>
                      </button>
                    </div>
                    <div className="p-10" ref={invoiceRef}>
                      <h1 className="text-3xl flex flex-row">
                        {coach?.firstName}{" "}
                        <p className="ml-2 text-[#fda4af]">{coach?.lastName}</p>
                      </h1>
                      <div className="flex justify-between mb-10 mt-10">
                        <div>
                          <b className="text-sm"> To,</b>
                          <br />
                          <b className="text-sm">
                            {" "}
                            Umn Khel Shiksha Private Limited
                          </b>
                          <p className="text-sm"> Vasant Vihar</p>
                          <p className="text-sm">
                            {" "}
                            Basant Lok Complex, Road 21
                          </p>
                          <p className="text-sm">New Delhi-110057</p>
                          <br />
                          <p className="text-sm"> GST ID: 07AADCU2822L1Z8</p>
                        </div>
                        <div className="flex flex-col">
                          <div className="flex flex-row">
                            <p className="text-sm font-bold">Date:</p>
                            <p className="text-sm ml-2">
                              {convertDateIntoIndianFormat(
                                new Date(singleReport?.date.split("T")[0])
                              )}
                            </p>
                          </div>
                          <br />
                          <div className="flex flex-row">
                            <p className="text-sm font-bold">Invoice No.</p>
                            <p className="text-sm ml-2">
                              {singleReport?.bookingId}_{singleReportIndex + 1}
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="mb-10">
                        <div className="flex justify-between">
                          <p className="text-sm font-light">
                            <u>DESCRIPTION</u>
                          </p>
                          <p className="text-sm font-light">
                            <u>AMOUNT</u>
                          </p>
                        </div>
                        <br />
                        <div className="flex justify-between">
                          <p className="text-sm">{singleReport?.courseName}</p>
                          <p className="text-sm">
                            ₹
                            {singleReport?.coachFeesAfterCancelation.toFixed(2)}
                          </p>
                        </div>
                        <div className="flex justify-between">
                          <p className="text-sm">TDS Deduction(1%)</p>
                          <p className="text-sm">
                            ₹{singleReport?.tds.toFixed(2)}
                          </p>
                        </div>
                        <br />
                        <div className="flex justify-between">
                          <p className="text-sm font-light">Total:</p>
                          <p className="text-sm font-light">
                            ₹{singleReport?.amountReceived.toFixed(2)}
                          </p>
                        </div>
                      </div>

                      <div className="mb-10">
                        <p className="text-sm">
                          Amount (in words): Rupees{" "}
                          {numberToWords(
                            singleReport?.amountReceived.toFixed(2)
                          )}{" "}
                          only.
                        </p>
                      </div>

                      <div className="mb-10">
                        <p className="text-sm font-bold">Payment Details:</p>
                        <p className="text-sm">
                          Pan card no - {coach?.kycDocuments?.documentNumber}
                        </p>
                        <p className="text-sm">
                          Beneficiary name -{" "}
                          {coach?.bankDetails?.accountHolderName}, <br />
                          Account no - {coach?.bankDetails?.accountNumber},
                        </p>
                        <p className="text-sm">
                          IFSC code - {coach?.bankDetails?.ifsc}
                        </p>
                      </div>

                      <div className="mb-10">
                        <p className="text-sm">Regards,</p>
                        <p className="text-sm">
                          {" "}
                          {coach?.firstName} {coach?.lastName}
                        </p>
                      </div>
                    </div>
                    <div>
                      <button
                        onClick={handlePrint}
                        className="flex text-sm font-medium text-indigo-600 hover:text-indigo-500 sm:block p-[1rem] fixed right-5"
                      >
                        Print invoice
                        <span aria-hidden="true"> &rarr;</span>
                      </button>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition.Root>
    </div>
  );
};

export default InvoiceTemplate;
