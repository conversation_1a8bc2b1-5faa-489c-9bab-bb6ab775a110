import axios from "axios";
import { NextResponse } from "next/server";
import { verifyToken } from "@/helpers/getDataFromToken";
import API from "@/components/API";

export async function POST(req) {
  try {
    let reqBody = await req.json();

    // console.log(reqBody);

    const token = req.cookies.get("coach-token")?.value || "";

    const payload = await verifyToken(token);
    // console.log(payload.id);

    const response = await axios.post(
      `${API}/api/events?coachId=${payload.id}`,
      reqBody,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );
    // console.log(response.data, "resp")

    return new NextResponse(JSON.stringify(response.data));
  } catch (error) {
    console.log("error 31");
    return new NextResponse(JSON.stringify({ error_add: error }));
  }
}
