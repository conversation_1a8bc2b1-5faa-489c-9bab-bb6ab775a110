import { Lato } from "next/font/google";
import "./globals.css";
import Layout from "@/components/Layout/Layout.jsx";
const lato = Lato({ weight: "400", subsets: ["latin"] });

export const metadata = {
  title: "Coach Dashboard",
  description: "Generated by create next app",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/favicon.png" type="image/x-icon" sizes="any" />
      </head>
      <body className={lato.className}>
        <Layout>{children}</Layout>
      </body>
    </html>
  );
}
