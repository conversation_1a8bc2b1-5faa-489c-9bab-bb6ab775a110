import API from "@/components/API";
import { NextResponse } from "next/server";
import axios from "axios";

export async function POST(req) {
  try {
    let reqBody = await req.json();

    // console.log(reqBody, "body");
    const token = req.cookies.get("coach-token")?.value || "";

    const getPreviewUrl = await axios.post(
      `${API}/api/coach/download`,
      { location: `${reqBody}` },
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );

    const previewUrl = getPreviewUrl.data.url;
    // console.log(previewUrl);
    return new NextResponse(JSON.stringify(previewUrl));
  } catch (error) {
    console.log(error);
    return new NextResponse(JSON.stringify({ error: error }));
  }
}
