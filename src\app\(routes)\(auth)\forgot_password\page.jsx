"use client";

import ErrorNotification from "@/components/Notification/ErrorNotification";
import SuccessNotification from "@/components/Notification/SuccessNotification";
import Image from "next/image";
import { useState } from "react";
import { useRouter } from "next/navigation";

export default function ForgetPassword() {
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [successNotification, setSuccessNotification] = useState(false);
  const [errorNotification, setErrorNotification] = useState(false);
  const [message, setMessage] = useState("");

  const router = useRouter();
  const emailRegExp = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;

  const sendResetUrl = async () => {
    try {
      setLoading(true);
      if (!emailRegExp.test(email)) {
        setErrorNotification(true);
        setMessage("Please enter a valid email address.");
        setTimeout(() => {
          setErrorNotification(false);
        }, 4000);
        setLoading(false);
        return;
      }
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");
      let requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: JSON.stringify({ email: email }),
        redirect: "follow",
      };
      const response = await fetch(`/api/forget_password`, requestOptions);
      const result = await response.json();
      setLoading(false);
      console.log(result);
      if (result && result?.message) {
        setSuccessNotification(true);
        setErrorNotification(false);
        setMessage(
          "We have sent a password reset link to the provided email address."
        );
        setEmail("");
        setTimeout(() => {
          setSuccessNotification(false);
        }, 4000);
      } else if (result.error == "User not found") {
        setSuccessNotification(false);
        setErrorNotification(true);
        setMessage(result.error);
        setTimeout(() => {
          setErrorNotification(false);
        }, 4000);
      } else {
        setSuccessNotification(false);
        setErrorNotification(true);
        setMessage("Something went wrong");
        setTimeout(() => {
          setErrorNotification(false);
        }, 4000);
      }
    } catch (error) {
      setLoading(false);
      console.log(error);
    }
  };

  return (
    <>
      {successNotification && <SuccessNotification message={message} />}

      {errorNotification && <ErrorNotification message={message} />}

      <div className="w-full px-4 py-8 lg:py-16 sm:px-0 md:px-4">
        <div className="border py-4 lg:pb-10 sm:w-6/12 lg:w-4/12 2xl:w-3/12 shadow rounded-lg mx-auto">
          <div className="sm:mx-auto sm:w-full sm:max-w-sm">
            <Image
              priority
             className="mx-auto h-15 md:w-[152px] w-[104px]"
            src="/MainKhelCoach.png"
              alt="Your Company"
              width={100}
              height={100}
            />
          </div>

          <div className="mt-10 sm:mx-auto sm:w-full sm:max-w-sm">
            <form className="space-y-6">
              <div>
                <label
                  htmlFor="email"
                  className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                >
                  Enter Email
                </label>
                <div className="mt-2">
                  <input
                    id="email"
                    name="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    type="email"
                    autoComplete="email"
                    placeholder="Enter your email"
                    className={`p-2 block w-full px-3 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6`}
                  />
                </div>
              </div>

              <div className="lg:pt-2">
                {loading ? (
                  <button
                    type="button"
                    disabled
                    className="flex w-full justify-center rounded-md bg-red-500 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-red-400 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-400"
                    // onClick={() => sendResetUrl()}
                  >
                    Loading...
                  </button>
                ) : (
                  <button
                    type="button"
                    className="flex w-full justify-center rounded-md bg-red-500 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-red-400 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-400"
                    onClick={() => sendResetUrl()}
                  >
                    Continue
                  </button>
                )}
              </div>
            </form>
          </div>
        </div>
      </div>
    </>
  );
}
