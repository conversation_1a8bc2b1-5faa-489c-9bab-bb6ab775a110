"use client";
import { XMarkIcon } from "@heroicons/react/24/solid";
import { useState, Fragment, useEffect } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import axios from "axios";
import API from "@/components/API";
import SuccessNotification from "@/components/Notification/SuccessNotification";
import ErrorNotification from "@/components/Notification/ErrorNotification";
import { MultiSelect } from "react-multi-select-component";
import { useRouter } from "next/navigation";
import "@/style/customStyle.css";
import VerificationBanner from "@/components/verificationBanner/VerificationBanner";
import TermsAndConditionModal from "@/components/TermsAndCondition/TermsAndConditionModal";

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

export default function CourseCreationTwo() {
  const [categories, setCategories] = useState([{}]);
  const [selectedImages, setSelectedImages] = useState([]);

  const [successNotification, setSuccessNotification] = useState(false);
  const [errorNotification, setErrorNotification] = useState(false);
  const [message, setMessage] = useState("");

  const [isEditable, setIsEditable] = useState(true);
  const [loading, setLoading] = useState(false);
  const [verificationModal, setVerificationModal] = useState(false);

  const [selected, setSelected] = useState([]);
  const [categoryError, setCategoryError] = useState(false);
  const [open, setOpen] = useState(false);
  const [saveData, setSaveData] = useState();

  const [awardImages, setAwardImages] = useState([]);
  const [playingExperienceImages, setPlayingExperienceImages] = useState([]);
  const [coachingExperienceImages, setCoachingExperienceImages] = useState([]);
  const [coachingQualificationsImages, setCoachingQualificationsImages] =
    useState([]);

  const router = useRouter();

  const getCategories = async () => {
    try {
      let response = await axios.get(`${API}/api/category`);
      // console.log(response.data, "ooooooo");
      setCategories(response.data.data);
    } catch (error) {
      console.log("error 48");
    }
  };

  useEffect(() => {
    getCategories();
  }, []);

  const updateDB = async (obj) => {
    try {
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      // const obj = {
      //   coachingQualifications: [...coachingQualificationsImages],
      //   coachingExperience: [...coachingExperienceImages],
      //   award: [...awardImages],
      //   playerExperience: [...playingExperienceImages],
      // };

      let requestOptions = {
        method: "PATCH",
        headers: myHeaders,
        body: JSON.stringify(obj),
      };
      let response = await fetch(`/api/coach_profile`, requestOptions);

      const result = await response.json();
    } catch (error) {
      console.log("error 77");
    }
  };

  const formik = useFormik({
    initialValues: {
      experience: "",
      language: "",
      sportsCategories: [],
      privacyPolicyAccepted: true,
      coachingQualifications: [{ description: "", image: "" }],
      coachingExperience: [{ description: "", image: "" }],
      playerExperience: [{ description: "", image: "" }],
      award: [{ description: "", image: "" }],
    },
    validationSchema: Yup.object().shape({
      experience: Yup.number("Only Number are allowed")
        .max(99, "can not more than 99 years")
        .required("This field is required"),
      language: Yup.string().required("Atleast one language is required"),
      // sportsCategories: Yup.array().required("This field is required"),
      // coachingQualifications: Yup.array().of(
      //   Yup.object().shape({
      //    qualificationName: Yup.string(),
      //   })
      // ),
    }),
    onSubmit: async (values) => {
      try {
        setLoading(true);
        // console.log(selected);

        if (selected.length == 0) {
          setCategoryError(true);
          setLoading(false);
          return;
        } else {
          let temp = [];
          selected &&
            selected.length > 0 &&
            selected.map((x) => {
              temp.push(x.value);
            });
          formik.setFieldValue("sportsCategories", temp);

          const filterEmptyFields = (array) => {
            return array.filter((item) => item.description || item.image);
          };

          // Apply the filter to the relevant fields
          const filteredValues = {
            coachingQualifications: filterEmptyFields(
              values.coachingQualifications
            ),
            coachingExperience: filterEmptyFields(values.coachingExperience),
            playerExperience: filterEmptyFields(values.playerExperience),
            award: filterEmptyFields(values.award),
          };

          let myHeaders = new Headers();
          myHeaders.append("Content-Type", "application/json");

          let requestOptions = {
            method: "PATCH",
            headers: myHeaders,
            body: JSON.stringify({
              ...values,
              ...filteredValues,
            }),
          };

          const response = await fetch(`/api/coach_profile`, requestOptions);

          const result = await response.json();
          // console.log(result, "result");

          if (!result.error) {
            setSuccessNotification(true);
            setIsEditable(false);

            if (result.status === "active") {
              setTimeout(() => {
                setSuccessNotification(false);
                router.push("/calendar");
              }, 2000);
              setMessage("Details Saved Successfully.");
            } else {
              setSuccessNotification(false);
              setVerificationModal(true);
              setMessage(
                "Your information has been successfully saved. Your application is currently under review, and we will notify you once it has been approved."
              );
            }
          }
          if (result.error) {
            setErrorNotification(true);
            setMessage(result.error);
            setTimeout(() => {
              setErrorNotification(false);
            }, 3000);
          }
        }

        // console.log(result, "pp");
        // router.push(`/profile/coach_creation_3/${id}`);
      } catch (error) {
        console.error(error, "oo");
      }
    },
  });

  const handleFileChange = async (e, fieldName, index) => {
    // console.log(e, fieldName, index, "oo");
    try {
      const file = e.currentTarget.files[0];

      if (file && file.size > 10 * 1024 * 1024) {
        setErrorNotification(true);
        setMessage("Please select a file less than 10 MB.");
        setTimeout(() => {
          setErrorNotification(false);
        }, 3000);
        return;
      }

      const formData = new FormData();
      formData.append("image", file);

      const response = await axios.post(
        `${API}/api/coach/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      const url = response?.data?.url;

      let obj = {
        coachingQualifications: [...coachingQualificationsImages],
        coachingExperience: [...coachingExperienceImages],
        award: [...awardImages],
        playerExperience: [...playingExperienceImages],
      };

      if (fieldName === "award") {
        await formik.setFieldValue(`${fieldName}.${index}.image`, url);

        const updatedAwardImages = [...awardImages];
        updatedAwardImages[index] = {
          description: formik.values.award[index].description,
          image: url,
        };
        setAwardImages([...updatedAwardImages]);
        obj = {
          coachingQualifications: [...coachingQualificationsImages],
          coachingExperience: [...coachingExperienceImages],
          award: updatedAwardImages,
          playerExperience: [...playingExperienceImages],
        };
      }

      if (fieldName === "playerExperience") {
        await formik.setFieldValue(`${fieldName}.${index}.image`, url);
        const updatedPlayingExperienceImages = [...playingExperienceImages];
        updatedPlayingExperienceImages[index] = {
          description: formik.values.playerExperience[index].description,
          image: url,
        };
        setPlayingExperienceImages([...updatedPlayingExperienceImages]);
        obj = {
          coachingQualifications: [...coachingQualificationsImages],
          coachingExperience: [...coachingExperienceImages],
          award: [...awardImages],
          playerExperience: updatedPlayingExperienceImages,
        };
      }
      if (fieldName === "coachingExperience") {
        await formik.setFieldValue(`${fieldName}.${index}.image`, url);

        const updatedCoachingExperienceImages = [...coachingExperienceImages];
        updatedCoachingExperienceImages[index] = {
          description: formik.values.coachingExperience[index].description,
          image: url,
        };
        setCoachingExperienceImages([...updatedCoachingExperienceImages]);

        obj = {
          coachingQualifications: [...coachingQualificationsImages],
          coachingExperience: updatedCoachingExperienceImages,
          award: [...awardImages],
          playerExperience: [...playingExperienceImages],
        };
      }
      if (fieldName === "coachingQualifications") {
        await formik.setFieldValue(`${fieldName}.${index}.image`, url);

        const updatedCoachingQualificationsImages = [
          ...coachingQualificationsImages,
        ];
        updatedCoachingQualificationsImages[index] = {
          description: formik.values.coachingQualifications[index].description,
          image: url,
        };
        setCoachingQualificationsImages([
          ...updatedCoachingQualificationsImages,
        ]);
        obj = {
          coachingQualifications: updatedCoachingQualificationsImages,
          coachingExperience: [...coachingExperienceImages],
          award: [...awardImages],
          playerExperience: [...playingExperienceImages],
        };
      }

      await updateDB(obj);
    } catch (error) {
      console.log(error.response.data.error);
    }
  };

  const deleteImageFiles = async (url, fieldName, index) => {
    try {
      const formData = new FormData();
      formData.append("url", url);

      const response = await axios.post(
        `${API}/api/coach/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      const resp = response?.data;

      let obj = {
        coachingQualifications: [...coachingQualificationsImages],
        coachingExperience: [...coachingExperienceImages],
        award: [...awardImages],
        playerExperience: [...playingExperienceImages],
      };

      if (fieldName === "award") {
        await formik.setFieldValue(`${fieldName}.${index}.image`, "");
        const updatedAwardImages = [...awardImages];
        updatedAwardImages[index] = {
          description: formik.values.award[index].description,
          image: "",
        };
        setAwardImages([...updatedAwardImages]);
        obj = {
          coachingQualifications: [...coachingQualificationsImages],
          coachingExperience: [...coachingExperienceImages],
          award: updatedAwardImages,
          playerExperience: [...playingExperienceImages],
        };
      }
      if (fieldName === "playerExperience") {
        await formik.setFieldValue(`${fieldName}.${index}.image`, "");
        const updatedPlayingExperienceImages = [...playingExperienceImages];
        updatedPlayingExperienceImages[index] = {
          description: formik.values.playerExperience[index].description,
          image: "",
        };
        setPlayingExperienceImages([...updatedPlayingExperienceImages]);
        obj = {
          coachingQualifications: [...coachingQualificationsImages],
          coachingExperience: [...coachingExperienceImages],
          award: [...awardImages],
          playerExperience: updatedPlayingExperienceImages,
        };
      }
      if (fieldName === "coachingExperience") {
        await formik.setFieldValue(`${fieldName}.${index}.image`, "");
        const updatedCoachingExperienceImages = [...coachingExperienceImages];
        updatedCoachingExperienceImages[index] = {
          description: formik.values.coachingExperience[index].description,
          image: "",
        };
        setCoachingExperienceImages([...updatedCoachingExperienceImages]);

        obj = {
          coachingQualifications: [...coachingQualificationsImages],
          coachingExperience: updatedCoachingExperienceImages,
          award: [...awardImages],
          playerExperience: [...playingExperienceImages],
        };
      }
      if (fieldName === "coachingQualifications") {
        await formik.setFieldValue(`${fieldName}.${index}.image`, "");
        const updatedCoachingQualificationsImages = [
          ...coachingQualificationsImages,
        ];
        updatedCoachingQualificationsImages[index] = {
          description: formik.values.coachingQualifications[index].description,
          image: "",
        };
        setCoachingQualificationsImages([
          ...updatedCoachingQualificationsImages,
        ]);
        obj = {
          coachingQualifications: updatedCoachingQualificationsImages,
          coachingExperience: [...coachingExperienceImages],
          award: [...awardImages],
          playerExperience: [...playingExperienceImages],
        };
      }

      await updateDB(obj);
    } catch (error) {
      console.log(error);
    }
  };

  const addCoachingExperience = () => {
    formik.setValues({
      ...formik.values,
      coachingExperience: [
        ...formik.values.coachingExperience,
        {
          description: "",
          image: "",
        },
      ],
    });
    setCoachingExperienceImages([
      ...coachingExperienceImages,
      {
        description: "",
        image: "",
      },
    ]);
  };

  const addPlayingExperience = () => {
    formik.setValues({
      ...formik.values,
      playerExperience: [
        ...formik.values.playerExperience,
        {
          playerExperience: "",
          image: "",
        },
      ],
    });
    setPlayingExperienceImages([
      ...playingExperienceImages,
      {
        description: "",
        image: "",
      },
    ]);
  };

  const addAwards = () => {
    formik.setValues({
      ...formik.values,
      award: [
        ...formik.values.award,
        {
          awardName: "",
          image: "",
        },
      ],
    });
    setAwardImages([
      ...awardImages,
      {
        description: "",
        image: "",
      },
    ]);
  };

  const addQualification = () => {
    formik.setValues({
      ...formik.values,
      coachingQualifications: [
        ...formik.values.coachingQualifications,
        {
          description: "",
          image: "",
        },
      ],
    });
    setCoachingQualificationsImages([
      ...coachingQualificationsImages,
      {
        description: "",
        image: "",
      },
    ]);
  };

  const getCoachDetails = async () => {
    try {
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let requestOptions = {
        method: "GET",
        headers: myHeaders,
      };

      const response = await fetch(`/api/coach_profile`, requestOptions);

      const result = await response.json();
      let temp = [];
      if (!result.error) {
        formik.setValues({
          ...formik.values,
          ...result,
        });
        if (
          result.experience &&
          result.experience !== "" &&
          result.authStatus !== "authorized"
        ) {
          setIsEditable(false);
          setVerificationModal(true);
          setMessage(
            "Your information has been successfully saved. Your application is currently under review, and we will notify you once it has been approved."
          );
        }
        result.sportsCategories.map((x) => {
          temp.push({ label: x, value: x });
        });
        setSelected(temp);

        // console.log(...result.award, "result awards in ");

        setCoachingQualificationsImages([...result.coachingQualifications]);
        setCoachingExperienceImages([...result.coachingExperience]);
        setAwardImages([...result.award]);
        setPlayingExperienceImages([...result.playerExperience]);
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getCoachDetails();
  }, []);

  const handlePolicyModal = (formik) => {
    if (Object.keys(formik.errors).length === 0 && selected.length !== 0) {
      setSaveData(formik);
      setOpen(true);
    }else{
      if (selected.length == 0) {
        setCategoryError(true);
        setLoading(false);
      }
      formik.handleSubmit();
    }

  };  

  return (
    <>
      <title>
        {!isEditable
          ? "Coach Signup - Professional details"
          : "Coach Profile - Professional details"}
      </title>

      {verificationModal && (
        <VerificationBanner
          verificationModal={verificationModal}
          setVerificationModal={setVerificationModal}
          message={message}
        />
      )}

      <TermsAndConditionModal
        open={open}
        setOpen={setOpen}
        saveData={saveData}
      />

      {successNotification && <SuccessNotification message={message} />}

      {errorNotification && <ErrorNotification message={message} />}
      <form>
        <fieldset disabled={!isEditable}>
          <div className="overflow-hidden bg-white shadow sm:rounded-lg">
            <div className="border-t border-gray-100">
              <dl className="divide-y divide-gray-100">
                <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-semibold tracking-wide text-gray-900 required">
                    Experience
                  </dt>
                  <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                    <div className="grid grid-cols-1 gap-x-8 gap-y-8  md:grid-cols-1">
                      <div>
                        <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                          <div className="sm:col-span-6">
                            <label
                              htmlFor="experience"
                              className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                            >
                              No. of Years of Experience
                            </label>
                            <div className="mt-2">
                              <input
                                type="number"
                                name="experience"
                                id="experience"
                                autoComplete="none"
                                onFocus={(e) =>
                                  e.target.addEventListener(
                                    "wheel",
                                    function (e) {
                                      e.preventDefault();
                                    },
                                    { passive: false }
                                  )
                                }
                                placeholder="Enter no. of experience"
                                className=" px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                {...formik.getFieldProps("experience")}
                              />
                              <style jsx>{`
                                input[type="number"]::-webkit-inner-spin-button,
                                input[type="number"]::-webkit-outer-spin-button {
                                  -webkit-appearance: none;
                                  margin: 0;
                                }

                                input[type="number"] {
                                  -moz-appearance: textfield;
                                }
                              `}</style>
                              {formik.touched.experience &&
                                formik.errors.experience && (
                                  <div className="text-red-500">
                                    {formik.errors.experience}
                                  </div>
                                )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </dd>
                </div>
                <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-semibold tracking-wide text-gray-900 required">
                    Language
                  </dt>
                  <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                    <div className="grid grid-cols-1 gap-x-8 gap-y-8  md:grid-cols-1">
                      <div>
                        <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                          <div className="sm:col-span-6">
                            <label
                              htmlFor="language"
                              className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                            >
                              Language
                            </label>
                            <div className="mt-2">
                              <input
                                type="text"
                                name="language"
                                id="language"
                                autoComplete="none"
                                placeholder="Enter languages separated by commma( , )"
                                className="block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                {...formik.getFieldProps("language")}
                              />
                              {formik.touched.language &&
                                formik.errors.language && (
                                  <div className="text-red-500">
                                    {formik.errors.language}
                                  </div>
                                )}
                            </div>
                            {/* <div className="mt-2.5">
                            <button
                              type="button"
                              className="rounded-md bg-white px-8 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                              // onClick={addBtnHandler}
                            >
                              Add
                            </button>
                          </div> */}
                          </div>
                        </div>
                      </div>
                    </div>
                  </dd>
                </div>
                <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-semibold tracking-wide text-gray-900 required">
                    Sports Category
                  </dt>
                  <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                    <div className="grid grid-cols-1 gap-x-8 gap-y-8  md:grid-cols-1">
                      <div>
                        <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                          <div className="sm:col-span-6">
                            <MultiSelect
                              name=" sportsCategories"
                              disabled={!isEditable}
                              options={categories?.map((category) => ({
                                label: category.name,
                                value: category.name,
                              }))}
                              value={selected}
                              onChange={(e) => {
                                setSelected(e);
                                if (e.length > 0) {
                                  let temp = [];
                                  e.map((x) => {
                                    temp.push(x.value);
                                  });
                                  formik.setFieldValue(
                                    "sportsCategories",
                                    temp
                                  );
                                  setCategoryError(false);
                                } else {
                                  setCategoryError(true);
                                }
                              }}
                              labelledBy="Select the categories"
                              // {...formik.getFieldProps(" sportsCategories")}
                              // className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />

                            {/* <select
                              name=" sportsCategories"
                              multiple
                              id=" sportsCategories"
                              autoComplete="family-name"
                              {...formik.getFieldProps(" sportsCategories")}
                              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            >
                              <option value="">Select Category</option>
                              {categories &&
                                categories.length > 0 &&
                                categories.map((x, idx) => (
                                  <option key={idx} value={x.name}>
                                    {x.name}
                                  </option>
                                ))}
                            </select> */}
                            {/* {formik.touched.sportsCategories &&
                              formik.errors.sportsCategories && ( */}
                            {categoryError && (
                              <div className="text-red-500">
                                Categories are required
                              </div>
                            )}
                            {/* )} */}
                          </div>
                        </div>
                      </div>
                    </div>
                  </dd>
                </div>
                <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-semibold tracking-wide text-gray-900">
                    Coaching Qualification
                  </dt>
                  <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                    <div className="grid grid-cols-1 gap-x-8 gap-y-8  md:grid-cols-1">
                      <div>
                        {formik.values.coachingQualifications.map(
                          (qualification, index) => (
                            <div
                              key={index}
                              className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6"
                            >
                              <div className="sm:col-span-4">
                                <div>
                                  <label
                                    htmlFor={`coachingQualifications.${index}.description`}
                                    className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize flex-row"
                                  >
                                    <span>Qualification {index + 1}</span>
                                    {index + 1 > 1 ? (
                                      <button
                                        type="button"
                                        className="rounded-full bg-red-600 p-1.5 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 float-right"
                                        onClick={() =>
                                          formik.setValues((prevState) => ({
                                            ...prevState,
                                            coachingQualifications:
                                              prevState.coachingQualifications.filter(
                                                (_, i) => i !== index
                                              ),
                                          }))
                                        }
                                      >
                                        <XMarkIcon
                                          className="h-3 w-3"
                                          aria-hidden="true"
                                        />
                                      </button>
                                    ) : null}
                                  </label>
                                  <div className="mt-2">
                                    <textarea
                                      rows={4}
                                      name={`coachingQualifications.${index}.description`}
                                      id={`coachingQualifications.${index}.description`}
                                      className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6"
                                      {...formik.getFieldProps(
                                        `coachingQualifications.${index}.description`
                                      )}
                                    />
                                    {formik.touched.coachingQualifications?.[
                                      index
                                    ]?.description &&
                                      formik.errors.coachingQualifications?.[
                                        index
                                      ]?.description && (
                                        <div className="text-red-500">
                                          {
                                            formik.errors
                                              .coachingQualifications?.[index]
                                              ?.description
                                          }
                                        </div>
                                      )}
                                  </div>
                                </div>
                              </div>
                              <div className="sm:col-span-2 flex items-end">
                                <div className="col-span-full bg-white rounded-sm">
                                  <div className="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-5 text-center hover:border-gray-400">
                                    {formik.values.coachingQualifications[index]
                                      ?.image === "" && (
                                      <label
                                        htmlFor={`coachingQualifications.${index}.image`}
                                        className="cursor-pointer flex flex-col items-center"
                                        onClick={(e) => e.stopPropagation()}
                                      >
                                        {/* SVG icon */}
                                        <svg
                                          xmlns="http://www.w3.org/2000/svg"
                                          fill="none"
                                          viewBox="0 0 24 24"
                                          strokeWidth={1.5}
                                          stroke="currentColor"
                                          className="w-8 h-8"
                                        >
                                          <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
                                          />
                                        </svg>

                                        <span className=" block text-xs text-gray-500">
                                          <span className="font-semibold text-blue-500">
                                            Upload a file
                                          </span>
                                          <br /> PNG, JPG, GIF up to 10MB
                                        </span>
                                        <input
                                          id={`coachingQualifications.${index}.image`}
                                          type="file"
                                          name={`coachingQualifications.${index}.image`}
                                          accept="image/*"
                                          // value={`coachingQualifications.${index}.image`}
                                          onChange={(e) =>
                                            handleFileChange(
                                              e,
                                              "coachingQualifications",
                                              index
                                            )
                                          }
                                          className="hidden"
                                          // {...formik.getFieldProps(
                                          //   `coachingQualifications.${index}.image`
                                          // )}
                                        />
                                        {formik.touched
                                          .coachingQualifications?.[index]
                                          ?.image &&
                                          formik.errors
                                            .coachingQualifications?.[index]
                                            ?.image && (
                                            <div className="text-red-500">
                                              {
                                                formik.errors
                                                  .coachingQualifications?.[
                                                  index
                                                ]?.image
                                              }
                                            </div>
                                          )}
                                      </label>
                                    )}
                                    <div>
                                      {coachingQualificationsImages.length >
                                        0 &&
                                        coachingQualificationsImages[index]
                                          ?.image !== "" && (
                                          <div className="mt-1">
                                            <div className="relative">
                                              <img
                                                src={
                                                  coachingQualificationsImages[
                                                    index
                                                  ]?.image
                                                }
                                                alt="Selected Image"
                                                className={
                                                  coachingQualificationsImages[
                                                    index
                                                  ]?.image &&
                                                  coachingQualificationsImages[
                                                    index
                                                  ].image !== ""
                                                    ? "w-full h-full object-cover rounded"
                                                    : "hidden"
                                                }
                                              />
                                              <button
                                                onClick={(e) => {
                                                  e.preventDefault();
                                                  formik.setFieldValue(
                                                    `coachingQualifications.${index}.image`,
                                                    ""
                                                  );
                                                  setCoachingQualificationsImages(
                                                    coachingQualificationsImages.filter(
                                                      (x, idx) => idx !== index
                                                    )
                                                  );
                                                  deleteImageFiles(
                                                    qualification.image,
                                                    "coachingQualifications",
                                                    index
                                                  );
                                                }}
                                                className={
                                                  coachingQualificationsImages[
                                                    index
                                                  ]?.image &&
                                                  coachingQualificationsImages[
                                                    index
                                                  ].image !== ""
                                                    ? "absolute top-2 right-2 bg-white p-1 rounded-full hover:bg-gray-200 focus:outline-none z-10"
                                                    : "hidden"
                                                }
                                              >
                                                <svg
                                                  xmlns="http://www.w3.org/2000/svg"
                                                  fill="none"
                                                  viewBox="0 0 24 24"
                                                  stroke="currentColor"
                                                  className="h-4 w-4 text-red-500"
                                                >
                                                  <path
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    d="M6 18L18 6M6 6l12 12"
                                                  />
                                                </svg>
                                              </button>
                                            </div>
                                          </div>
                                        )}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )
                        )}
                        <div className="mt-2.5">
                          <button
                            type="button"
                            className="rounded-md bg-white px-8 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                            onClick={addQualification}
                          >
                            Add
                          </button>
                        </div>
                      </div>
                    </div>
                  </dd>
                </div>

                <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-semibold tracking-wide text-gray-900">
                    Coaching Experience
                  </dt>
                  <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                    <div className="grid grid-cols-1 gap-x-8 gap-y-8  md:grid-cols-1">
                      <div>
                        {formik.values.coachingExperience.map(
                          (experience, index) => (
                            <div
                              key={index}
                              className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6"
                            >
                              <div className="sm:col-span-4">
                                <div>
                                  <label
                                    htmlFor={`coachingExperience.${index}.description`}
                                    className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                                  >
                                    Experience {index + 1}
                                    {index + 1 > 1 ? (
                                      <button
                                        type="button"
                                        className="rounded-full bg-red-600 p-1.5 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 float-right"
                                        onClick={() =>
                                          formik.setValues((prevState) => ({
                                            ...prevState,
                                            coachingExperience:
                                              prevState.coachingExperience.filter(
                                                (_, i) => i !== index
                                              ),
                                          }))
                                        }
                                      >
                                        <XMarkIcon
                                          className="h-3 w-3"
                                          aria-hidden="true"
                                        />
                                      </button>
                                    ) : null}
                                  </label>
                                  <div className="mt-2">
                                    <textarea
                                      rows={4}
                                      name={`coachingExperience.${index}.description`}
                                      id={`coachingExperience.${index}.description`}
                                      className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6"
                                      {...formik.getFieldProps(
                                        `coachingExperience.${index}.description`
                                      )}
                                    />
                                    {formik.touched.coachingExperience?.[index]
                                      ?.description &&
                                      formik.errors.coachingExperience?.[index]
                                        ?.description && (
                                        <div className="text-red-500">
                                          {
                                            formik.errors.coachingExperience?.[
                                              index
                                            ]?.description
                                          }
                                        </div>
                                      )}
                                  </div>
                                </div>
                              </div>
                              <div className="sm:col-span-2 flex items-end">
                                <div className="col-span-full bg-white rounded-sm">
                                  <div className="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-5 text-center hover:border-gray-400">
                                    {formik.values.coachingExperience[index]
                                      ?.image === "" && (
                                      <label
                                        htmlFor={`coachingExperience.${index}.image`}
                                        className="cursor-pointer flex flex-col items-center"
                                        onClick={(e) => e.stopPropagation()}
                                      >
                                        {/* SVG icon */}
                                        <svg
                                          xmlns="http://www.w3.org/2000/svg"
                                          fill="none"
                                          viewBox="0 0 24 24"
                                          strokeWidth={1.5}
                                          stroke="currentColor"
                                          className="w-8 h-8"
                                        >
                                          <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
                                          />
                                        </svg>

                                        <span className=" block text-xs text-gray-500">
                                          <span className="font-semibold text-blue-500">
                                            Upload a file
                                          </span>
                                          <br /> PNG, JPG, GIF up to 10MB
                                        </span>
                                        <input
                                          id={`coachingExperience.${index}.image`}
                                          type="file"
                                          name={`coachingExperience.${index}.image`}
                                          accept="image/*"
                                          onChange={(e) =>
                                            handleFileChange(
                                              e,
                                              "coachingExperience",
                                              index
                                            )
                                          }
                                          className="hidden"
                                        />
                                      </label>
                                    )}
                                    <div>
                                      {coachingExperienceImages.length > 0 && (
                                        <div className="mt-1">
                                          <div className="relative">
                                            {/* {console.log(index, "index here")} */}
                                            <img
                                              src={
                                                coachingExperienceImages[index]
                                                  ?.image
                                              }
                                              alt="Selected Image"
                                              className={
                                                coachingExperienceImages[index]
                                                  ?.image &&
                                                coachingExperienceImages[index]
                                                  .image !== ""
                                                  ? "w-full h-full object-cover rounded"
                                                  : "hidden"
                                              }
                                            />
                                            <button
                                              onClick={(e) => {
                                                e.preventDefault();
                                                formik.setFieldValue(
                                                  `coachingExperience.${index}.image`,
                                                  ""
                                                );
                                                setCoachingExperienceImages(
                                                  coachingExperienceImages.filter(
                                                    (x, idx) => idx !== index
                                                  )
                                                );
                                                deleteImageFiles(
                                                  experience.image,
                                                  "coachingExperience",
                                                  index
                                                );
                                              }}
                                              className={
                                                coachingExperienceImages[index]
                                                  ?.image &&
                                                coachingExperienceImages[index]
                                                  .image !== ""
                                                  ? "absolute top-2 right-2 bg-white p-1 rounded-full hover:bg-gray-200 focus:outline-none z-10"
                                                  : "hidden"
                                              }
                                            >
                                              <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke="currentColor"
                                                className="h-4 w-4 text-red-500"
                                              >
                                                <path
                                                  strokeLinecap="round"
                                                  strokeLinejoin="round"
                                                  d="M6 18L18 6M6 6l12 12"
                                                />
                                              </svg>
                                            </button>
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )
                        )}
                        <div className="mt-2.5">
                          <button
                            type="button"
                            className="rounded-md bg-white px-8 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                            onClick={addCoachingExperience}
                          >
                            Add
                          </button>
                        </div>
                      </div>
                    </div>
                  </dd>
                </div>

                <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-semibold tracking-wide text-gray-900">
                    Playing Experience
                  </dt>
                  <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                    <div className="grid grid-cols-1 gap-x-8 gap-y-8  md:grid-cols-1">
                      <div>
                        {formik.values.playerExperience.map(
                          (playingExp, index) => (
                            <div
                              key={index}
                              className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6"
                            >
                              <div className="sm:col-span-4">
                                <div>
                                  <label
                                    htmlFor={`playerExperience.${index}.description`}
                                    className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                                  >
                                    Playing Experience {index + 1}
                                    {index + 1 > 1 ? (
                                      <button
                                        type="button"
                                        className="rounded-full bg-red-600 p-1.5 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 float-right"
                                        onClick={() =>
                                          formik.setValues((prevState) => ({
                                            ...prevState,
                                            playerExperience:
                                              prevState.playerExperience.filter(
                                                (_, i) => i !== index
                                              ),
                                          }))
                                        }
                                      >
                                        <XMarkIcon
                                          className="h-3 w-3"
                                          aria-hidden="true"
                                        />
                                      </button>
                                    ) : null}
                                  </label>
                                  <div className="mt-2">
                                    <textarea
                                      rows={4}
                                      name={`playerExperience.${index}.description`}
                                      id={`playerExperience.${index}.description`}
                                      className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6"
                                      {...formik.getFieldProps(
                                        `playerExperience.${index}.description`
                                      )}
                                    />
                                    {formik.touched.playerExperience?.[index]
                                      ?.description &&
                                      formik.errors.playerExperience?.[index]
                                        ?.description && (
                                        <div className="text-red-500">
                                          {
                                            formik.errors.playerExperience?.[
                                              index
                                            ]?.description
                                          }
                                        </div>
                                      )}
                                  </div>
                                </div>
                              </div>
                              <div className="sm:col-span-2 flex items-end">
                                <div className="col-span-full bg-white rounded-sm">
                                  <div className="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-5 text-center hover:border-gray-400">
                                    {formik.values.playerExperience[index]
                                      ?.image === "" && (
                                      <label
                                        htmlFor="imageInput"
                                        className="cursor-pointer flex flex-col items-center"
                                        onClick={(e) => e.stopPropagation()}
                                      >
                                        {/* SVG icon */}
                                        <svg
                                          xmlns="http://www.w3.org/2000/svg"
                                          fill="none"
                                          viewBox="0 0 24 24"
                                          strokeWidth={1.5}
                                          stroke="currentColor"
                                          className="w-8 h-8"
                                        >
                                          <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
                                          />
                                        </svg>

                                        <span className=" block text-xs text-gray-500">
                                          <span className="font-semibold text-blue-500">
                                            Upload a file
                                          </span>
                                          <br /> PNG, JPG, GIF up to 10MB
                                        </span>
                                        <input
                                          id="imageInput"
                                          type="file"
                                          accept="image/*"
                                          onChange={(e) =>
                                            handleFileChange(
                                              e,
                                              "playerExperience",
                                              index
                                            )
                                          }
                                          className="hidden"
                                        />
                                      </label>
                                    )}
                                    <div>
                                      {playingExperienceImages.length > 0 && (
                                        <div className="mt-1">
                                          {/* {console.log(
                                            playingExperienceImages,
                                            "pp"
                                          )} */}
                                          <div className="relative">
                                            <img
                                              src={
                                                playingExperienceImages[index]
                                                  ?.image
                                              }
                                              alt="Selected Image"
                                              className={
                                                playingExperienceImages[index]
                                                  ?.image &&
                                                playingExperienceImages[index]
                                                  .image !== ""
                                                  ? "w-full h-full object-cover rounded"
                                                  : "hidden"
                                              }
                                            />
                                            <button
                                              onClick={(e) => {
                                                e.preventDefault();
                                                formik.setFieldValue(
                                                  `playerExperience.${index}.image`,
                                                  ""
                                                );
                                                setPlayingExperienceImages(
                                                  playingExperienceImages.filter(
                                                    (x, idx) => idx !== index
                                                  )
                                                );
                                                deleteImageFiles(
                                                  playingExp.image,
                                                  "playerExperience",
                                                  index
                                                );
                                              }}
                                              className={
                                                playingExperienceImages[index]
                                                  ?.image &&
                                                playingExperienceImages[index]
                                                  .image !== ""
                                                  ? "absolute top-2 right-2 bg-white p-1 rounded-full hover:bg-gray-200 focus:outline-none z-10"
                                                  : "hidden"
                                              }
                                            >
                                              <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke="currentColor"
                                                className="h-4 w-4 text-red-500"
                                              >
                                                <path
                                                  strokeLinecap="round"
                                                  strokeLinejoin="round"
                                                  d="M6 18L18 6M6 6l12 12"
                                                />
                                              </svg>
                                            </button>
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )
                        )}
                        <div className="mt-2.5">
                          <button
                            type="button"
                            className="rounded-md bg-white px-8 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                            onClick={addPlayingExperience}
                          >
                            Add
                          </button>
                        </div>
                      </div>
                    </div>
                  </dd>
                </div>

                <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-semibold tracking-wide text-gray-900">
                    Award
                  </dt>
                  <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                    <div className="grid grid-cols-1 gap-x-8 gap-y-8  md:grid-cols-1">
                      <div>
                        {formik.values.award.map((awrd, index) => (
                          <div
                            key={index}
                            className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6"
                          >
                            <div className="sm:col-span-4">
                              <div>
                                <label
                                  htmlFor={`award.${index}.description`}
                                  className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                                >
                                  Award {index + 1}
                                  {index + 1 > 1 ? (
                                    <button
                                      type="button"
                                      className="rounded-full bg-red-600 p-1.5 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 float-right"
                                      onClick={() =>
                                        formik.setValues((prevState) => ({
                                          ...prevState,
                                          award: prevState.award.filter(
                                            (_, i) => i !== index
                                          ),
                                        }))
                                      }
                                    >
                                      <XMarkIcon
                                        className="h-3 w-3"
                                        aria-hidden="true"
                                      />
                                    </button>
                                  ) : null}
                                </label>
                                <div className="mt-2">
                                  <textarea
                                    rows={4}
                                    name={`award.${index}.description`}
                                    id={`award.${index}.description`}
                                    className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6"
                                    {...formik.getFieldProps(
                                      `award.${index}.description`
                                    )}
                                  />
                                  {formik.touched.award?.[index]?.description &&
                                    formik.errors.award?.[index]
                                      ?.description && (
                                      <div className="text-red-500">
                                        {
                                          formik.errors.award?.[index]
                                            ?.description
                                        }
                                      </div>
                                    )}
                                </div>
                              </div>
                            </div>
                            <div className="sm:col-span-2 flex items-end">
                              <div className="col-span-full bg-white rounded-sm">
                                <div className="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-5 text-center hover:border-gray-400">
                                  {formik.values.award[index]?.image === "" && (
                                    <label
                                      htmlFor={`award.${index}.image`}
                                      className="cursor-pointer flex flex-col items-center"
                                      onClick={(e) => e.stopPropagation()}
                                    >
                                      {/* SVG icon */}
                                      <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        strokeWidth={1.5}
                                        stroke="currentColor"
                                        className="w-8 h-8"
                                      >
                                        <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
                                        />
                                      </svg>

                                      <span className=" block text-xs text-gray-500">
                                        <span className="font-semibold text-blue-500">
                                          Upload a file
                                        </span>
                                        <br /> PNG, JPG, GIF up to 10MB
                                      </span>
                                      <input
                                        id={`award.${index}.image`}
                                        type="file"
                                        name={`award.${index}.image`}
                                        accept="image/*"
                                        onChange={(e) =>
                                          handleFileChange(e, "award", index)
                                        }
                                        className="hidden"
                                        // {...formik.getFieldProps(
                                        //   `awards.${index}.image`
                                        // )}
                                      />
                                      {formik.touched.award?.[index]?.image &&
                                        formik.errors.award?.[index]?.image && (
                                          <div className="text-red-500">
                                            {
                                              formik.errors.award?.[index]
                                                ?.image
                                            }
                                          </div>
                                        )}
                                    </label>
                                  )}
                                  <div>
                                    {awardImages.length > 0 && (
                                      <div className="mt-1">
                                        {/* {console.log(
                                          awardImages[index]?.image,
                                          "award images",
                                          index
                                        )} */}
                                        <div className="relative">
                                          <img
                                            src={awardImages[index]?.image}
                                            alt="Selected Image"
                                            className={
                                              awardImages[index]?.image &&
                                              awardImages[index]?.image !== ""
                                                ? "w-full h-full object-cover rounded"
                                                : "hidden"
                                            }
                                          />
                                          <button
                                            onClick={(e) => {
                                              e.preventDefault();
                                              formik.setFieldValue(
                                                `award.${index}.image`,
                                                ""
                                              );
                                              setAwardImages(
                                                awardImages.filter(
                                                  (x, idx) => idx !== index
                                                )
                                              );
                                              deleteImageFiles(
                                                awrd.image,
                                                "award",
                                                index
                                              );
                                            }}
                                            className={
                                              awardImages[index]?.image &&
                                              awardImages[index]?.image !== ""
                                                ? "absolute top-2 right-2 bg-white p-1 rounded-full hover:bg-gray-200 focus:outline-none z-10"
                                                : "hidden"
                                            }
                                          >
                                            <svg
                                              xmlns="http://www.w3.org/2000/svg"
                                              fill="none"
                                              viewBox="0 0 24 24"
                                              stroke="currentColor"
                                              className="h-4 w-4 text-red-500"
                                            >
                                              <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                d="M6 18L18 6M6 6l12 12"
                                              />
                                            </svg>
                                          </button>
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                        <div className="mt-2.5">
                          <button
                            type="button"
                            className="rounded-md bg-white px-8 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                            onClick={addAwards}
                          >
                            Add
                          </button>
                        </div>
                      </div>
                    </div>
                  </dd>
                </div>
                {isEditable && (
                  <div className="mt-3 flex items-center justify-end gap-x-6 p-4">
                    <button
                      onClick={() => {
                        router.push("/login");
                      }}
                      type="button"
                      className="text-sm font-semibold leading-6 text-gray-900"
                    >
                      Cancel
                    </button>
                    {loading ? (
                      <button
                        disabled
                        type="button"
                        className="text-white px-3 py-2.5 bg-indigo-700 hover:bg-blue-800   font-medium rounded text-sm text-center mr-2 dark:bg-indigo-600 dark:hover:bg-indigo-700  inline-flex items-center"
                      >
                        <svg
                          aria-hidden="true"
                          role="status"
                          class="inline mr-3 w-4 h-4 text-white animate-spin"
                          viewBox="0 0 100 101"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                          ></path>
                          <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                          ></path>
                        </svg>
                        Loading...
                      </button>
                    ) : (
                      <button
                        type="submit"
                        className="rounded-md bg-sky-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-sky-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-sky-600"
                        onClick={(e) => {
                          e.preventDefault();
                          handlePolicyModal(formik);
                        }}
                      >
                        Save
                      </button>
                    )}
                  </div>
                )}
              </dl>
            </div>
          </div>
        </fieldset>
      </form>
    </>
  );
}
