"use client";

import React from "react";
import { Dialog } from "@headlessui/react";

const ConfirmationModal = ({ open, setOpen, saveData }) => {
    return (
        <Dialog open={open} onClose={() => false} className="relative z-10">
            <Dialog.Backdrop
                transition
                className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
            />

            <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
                <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                    <Dialog.Panel
                        transition
                        className="relative transform overflow-hidden w-full rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-20 sm:w-full sm:max-w-sm sm:p-6"
                    >
                        <div className="flex items-center justify-between border-b rounded-t">
                            <h3 className="text-lg font-medium text-gray-900">
                                Important Notice
                            </h3>
                        </div>

                        <div className="mt-3 text-base leading-relaxed text-gray-500">
                            Kindly make sure your KYC details are correct as changes will not be permitted after this stage.
                            Thank you.
                        </div>

                        <div className="flex justify-end gap-4 mt-4">
                            <button
                                type="button"
                                className="py-2 px-4 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg hover:bg-gray-100 focus:outline-none"
                                onClick={() => setOpen(false)}
                            >
                                Cancel
                            </button>
                            <button
                                type="button"
                                className="py-2 px-4 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none"
                                onClick={() => {
                                    setOpen(!open);
                                    saveData.handleSubmit();
                                }}
                            >
                                Save
                            </button>
                        </div>
                    </Dialog.Panel>
                </div>
            </div>
        </Dialog>
    );
};

export default ConfirmationModal;
