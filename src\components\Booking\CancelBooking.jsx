import React, { useState, useEffect, Fragment } from "react";
import { Dialog, Transition } from "@headlessui/react";
import moment from "moment-timezone";

const CancelBooking = ({
  cancelModal,
  setCancelModal,
  setSuccessNotification,
  setErrorNotification,
  selectedBooking,
  setMessage,
  selectedDates,
}) => {
  const [cancelDates, setCancelDates] = useState([]);
  const [classDates, setClassDates] = useState([]);
  const [buttonLoading, setButtonLoading] = useState(false);
  const [loading, setLoading] = useState(false);

  const filterDates = () => {
    if (
      selectedBooking &&
      selectedBooking.bookings &&
      selectedBooking.bookings.length > 0
    ) {
      let upcomingBookings = selectedBooking.bookings.filter(
        (x) =>
          x.status === "upcoming" &&
          x?.attendance === "NA" &&
          x?.coachAttendance === "NA"
      );

      upcomingBookings = upcomingBookings?.filter((x) => {
        let endDate = moment
          .tz(moment(selectedDates[0]), "Asia/Kolkata")
          .format()
          .split("T")[0];
        endDate = moment
          .tz(moment(`${endDate}T${x.endTime}`), "Asia/Kolkata")
          .format();
        const currentDate = moment.tz(moment(), "Asia/Kolkata").format();
        // console.log(currentDate, endDate);
        return moment(currentDate).isBefore(moment(endDate));
      });

      // console.log(upcomingBookings);
      setClassDates([...upcomingBookings]);
    }
  };

  useEffect(() => {
    filterDates();
  }, [selectedBooking]);

  const toggleDateSelection = (data) => {
    const existingIndex = cancelDates.findIndex(
      (item) =>
        item.bookingId === data.bookingId && item.classId === data.classId
    );

    if (existingIndex !== -1) {
      setCancelDates(cancelDates.filter((_, index) => index !== existingIndex));
    } else {
      setCancelDates([
        ...cancelDates,
        { bookingId: data.bookingId, classId: data.classId },
      ]);
    }
  };

  const handleCancelBooking = async () => {
    try {
      setButtonLoading(true);
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: JSON.stringify(cancelDates),
      };
      const response = await fetch(
        `/api/bookings/cancel-bookings`,
        requestOptions
      );

      const result = await response.json();
      // console.log(result, "result here");
      if (result.message == "Booking canceled successfully") {
        setCancelModal(false);
        setSuccessNotification(true);
        setTimeout(() => {
          setSuccessNotification(false);
          setErrorNotification(false);
        }, 3000);
        setErrorNotification(false);
        setMessage("Booking cancelled successfully");
        setButtonLoading(false);
      } else {
        setCancelModal(false);
        setSuccessNotification(false);
        setTimeout(() => {
          setSuccessNotification(false);
          setErrorNotification(false);
        }, 3000);
        setErrorNotification(true);
        setMessage("Please try again later");
        setButtonLoading(false);
      }
    } catch (error) {
      console.log(error);
      setSuccessNotification(false);
      setErrorNotification(false);
      setMessage("Something went wrong, Please try again later");
      setButtonLoading(false);
    }
  };

  // useEffect(() => {
  //   if (bookingId) getBookings();
  // }, [cancelModal]);

  return (
    <div>
      <Transition.Root show={cancelModal} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-10"
          onClose={() => setCancelModal(!cancelModal)}
        >
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
            <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                enterTo="opacity-100 translate-y-0 sm:scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              >
                <Dialog.Panel
                  className={`relative transform rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-sm sm:p-6 max-h-[300px] mt-10 overflow-auto max-sm:mb-[150px]`}
                >
                  <>
                    {loading ? (
                      <>
                        <div className="flex items-center justify-center h-screen">
                          <div
                            className="inline-block h-20 w-20 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"
                            role="status"
                          >
                            <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">
                              Loading...
                            </span>
                          </div>
                        </div>
                      </>
                    ) : (
                      <>
                        <div>
                          {classDates && classDates.length > 0 && (
                            <span className="text-bold text-[23px]">
                              Select the classes to cancel
                            </span>
                          )}
                          <br />
                          <br />
                          {classDates && !classDates.length > 0 && (
                            <span>No bookings to cancel</span>
                          )}
                          {classDates?.map((bookingTime, index) => {
                            const date = new Date(
                              selectedDates[0]
                            ).toLocaleDateString("en-IN", {
                              day: "numeric",
                              month: "short",
                              year: "numeric",
                            });
                            return (
                              <div
                                key={index}
                                className="flex items-center gap-4"
                              >
                                <input
                                  type="checkbox"
                                  id={`date-${index}`}
                                  disabled={bookingTime.status !== "upcoming"}
                                  checked={cancelDates.some(
                                    (item) =>
                                      item.bookingId === bookingTime.id &&
                                      item.classId === bookingTime.classId
                                  )}
                                  onChange={() =>
                                    toggleDateSelection({
                                      bookingId: bookingTime.id,
                                      classId: bookingTime.classId,
                                    })
                                  }
                                />
                                <label
                                  htmlFor={`date-${index}`}
                                  className="font-semibold text-gray-500"
                                >{`${date} (${bookingTime.startTime} - ${bookingTime.endTime})`}</label>
                              </div>
                            );
                          })}
                        </div>
                        <br />
                        <div className="flex float-right">
                          <button
                            type="button"
                            className="text-white bg-red-700 hover:bg-red-800 focus:outline-none focus:ring-4 focus:ring-red-300 font-medium rounded-full text-sm px-5 py-2.5 text-center me-2 mb-2 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900"
                            onClick={() => setCancelModal(false)}
                          >
                            Close
                          </button>
                          {buttonLoading ? (
                            <button
                              disabled
                              type="button"
                              className="text-white px-3 py-2.5 bg-green-700 hover:bg-green-800 w-full font-medium rounded text-sm text-center mr-2 dark:bg-green-600 dark:hover:bg-green-700  inline-flex items-center"
                            >
                              <svg
                                aria-hidden="true"
                                role="status"
                                class="inline mr-3 w-4 h-4 text-white animate-spin"
                                viewBox="0 0 100 101"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                  fill="#E5E7EB"
                                ></path>
                                <path
                                  d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                  fill="currentColor"
                                ></path>
                              </svg>
                              Loading...
                            </button>
                          ) : (
                            classDates &&
                            classDates.length > 0 && (
                              <button
                                type="button"
                                disabled={cancelDates.length == 0}
                                className="text-white bg-green-700 hover:bg-green-800 focus:outline-none focus:ring-4 focus:ring-green-300 font-medium rounded-full text-sm px-5 py-2.5 text-center me-2 mb-2 dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800"
                                onClick={() => handleCancelBooking()}
                              >
                                Confirm
                              </button>
                            )
                          )}
                        </div>
                      </>
                    )}
                  </>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition.Root>
    </div>
  );
};

export default CancelBooking;
