export const MOCK_EVENTS = [
  {
    id: 1,
    title: "Event 1",
    start: "2024-01-08T08:31:38",
    end: "2024-01-08T18:15:58",
    description:
      "Etiam vel augue. Vestibulum rutrum rutrum neque. Aenean auctor gravida sem.\n\nPraesent id massa id nisl venenatis lacinia. Aenean sit amet justo. Morbi ut odio.",
    color: "#C97D60",
  },
  // {
  //   id: 1100,
  //   title: "Event 1100",
  //   start: "2024-01-02T08:40:38",
  //   end: "2024-01-02T12:15:58",
  //   description:
  //     "Etiam vel augue. Vestibulum rutrum rutrum neque. Aenean auctor gravida sem.\n\nPraesent id massa id nisl venenatis lacinia. Aenean sit amet justo. Morbi ut odio.",
  //   color: "#C97D60",
  // },

  {
    id: 2,
    title: "Event 2",
    start: "2024-01-15T13:30:02",
    end: "2024-01-15T17:20:20",
    description:
      "Duis bibendum, felis sed interdum venenatis, turpis enim blandit mi, in porttitor pede justo eu massa. Donec dapibus. Duis at velit eu est congue elementum.\n\nIn hac habitasse platea dictumst. Morbi vestibulum, velit id pretium iaculis, diam erat fermentum justo, nec condimentum neque sapien placerat ante. Nulla justo.\n\nAliquam quis turpis eget elit sodales scelerisque. Mauris sit amet eros. Suspendisse accumsan tortor quis turpis.",
    color: "green",
  },
  {
    id: 3,
    title: "Event 3",
    start: "2024-01-16T10:53:12Z",
    end: "2024-01-16T14:22:42Z",
    description:
      "Integer tincidunt ante vel ipsum. Praesent blandit lacinia erat. Vestibulum sed magna at nunc commodo placerat.",
    color: "gray",
  },
  {
    id: 4,
    title: "Event 4",
    start: "2024-01-17T12:49:36Z",
    end: "2024-01-17T15:19:49Z",
    description:
      "Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Vivamus vestibulum sagittis sapien. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus.\n\nEtiam vel augue. Vestibulum rutrum rutrum neque. Aenean auctor gravida sem.\n\nPraesent id massa id nisl venenatis lacinia. Aenean sit amet justo. Morbi ut odio.",
    color: "indigo",
  },
  {
    id: 5,
    title: "Event 5",
    start: "2024-01-17T09:35:30Z",
    end: "2024-01-17T11:57:17Z",
    description:
      "Quisque id justo sit amet sapien dignissim vestibulum. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Nulla dapibus dolor vel est. Donec odio justo, sollicitudin ut, suscipit a, feugiat et, eros.\n\nVestibulum ac est lacinia nisi venenatis tristique. Fusce congue, diam id ornare imperdiet, sapien urna pretium nisl, ut volutpat sapien arcu sed augue. Aliquam erat volutpat.\n\nIn congue. Etiam justo. Etiam pretium iaculis justo.",
    color: "red",
  },
  {
    id: 6,
    title: "Event 6",
    start: "2024-01-18T10:26:34Z",
    end: "2024-01-18T17:43:06Z",
    description:
      "Curabitur in libero ut massa volutpat convallis. Morbi odio odio, elementum eu, interdum eu, tincidunt in, leo. Maecenas pulvinar lobortis est.\n\nPhasellus sit amet erat. Nulla tempus. Vivamus in felis eu sapien cursus vestibulum.\n\nProin eu mi. Nulla ac enim. In tempor, turpis nec euismod scelerisque, quam turpis adipiscing lorem, vitae mattis nibh ligula nec sem.",
    color: "green",
  },
  {
    id: 7,
    title: "Event 7",
    start: "2024-01-19T10:00:34Z",
    end: "2024-01-19T13:38:51Z",
    description:
      "Cras mi pede, malesuada in, imperdiet et, commodo vulputate, justo. In blandit ultrices enim. Lorem ipsum dolor sit amet, consectetuer adipiscing elit.\n\nProin interdum mauris non ligula pellentesque ultrices. Phasellus id sapien in sapien iaculis congue. Vivamus metus arcu, adipiscing molestie, hendrerit at, vulputate vitae, nisl.",
    color: "red",
  },
  {
    id: 8,
    title: "Event 8",
    start: "2024-01-19T15:00:34Z",
    end: "2024-01-19T17:38:51Z",
    description:
      "Praesent id massa id nisl venenatis lacinia. Aenean sit amet justo. Morbi ut odio.",
    color: "green",
  },
  {
    id: 9,
    title: "Event 9",
    start: "2024-01-20T15:00:34Z",
    end: "2024-01-20T17:38:51Z",
    description:
      "Fusce posuere felis sed lacus. Morbi sem mauris, laoreet ut, rhoncus aliquet, pulvinar sed, nisl. Nunc rhoncus dui vel sem.\n\nSed sagittis. Nam congue, risus semper porta volutpat, quam pede lobortis ligula, sit amet eleifend pede libero quis orci. Nullam molestie nibh in lectus.\n\nPellentesque at nulla. Suspendisse potenti. Cras in purus eu magna vulputate luctus.",
    color: "#C97D60",
  },
  {
    id: 10,
    title: "Event 10",
    start: "2024-01-14T15:00:15Z",
    end: "2024-01-14T17:00:15Z",
    description:
      "Praesent blandit. Nam nulla. Integer pede justo, lacinia eget, tincidunt eget, tempus vel, pede.",
    color: "gray",
  },
  {
    id: 11,
    title: "Event 11",
    start: "2024-01-21T14:33:28Z",
    end: "2024-01-21T18:02:09Z",
    description:
      "Proin interdum mauris non ligula pellentesque ultrices. Phasellus id sapien in sapien iaculis congue. Vivamus metus arcu, adipiscing molestie, hendrerit at, vulputate vitae, nisl.\n\nAenean lectus. Pellentesque eget nunc. Donec quis orci eget orci vehicula condimentum.",
    color: "#C97D60",
  },
  {
    id: 12,
    title: "Event 12",
    start: "2024-01-22T18:33:28Z",
    end: "2024-01-22T21:02:09Z",
    description:
      "Maecenas tristique, est et tempus semper, est quam pharetra magna, ac consequat metus sapien ut nunc. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Mauris viverra diam vitae quam. Suspendisse potenti.\n\nNullam porttitor lacus at turpis. Donec posuere metus vitae ipsum. Aliquam non mauris.",
    color: "red",
  },
  {
    id: 13,
    title: "Event 13",
    start: "2024-01-23T18:33:28Z",
    end: "2024-01-23T21:02:09Z",
    description:
      "Quisque id justo sit amet sapien dignissim vestibulum. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Nulla dapibus dolor vel est. Donec odio justo, sollicitudin ut, suscipit a, feugiat et, eros.\n\nVestibulum ac est lacinia nisi venenatis tristique. Fusce congue, diam id ornare imperdiet, sapien urna pretium nisl, ut volutpat sapien arcu sed augue. Aliquam erat volutpat.",
    color: "gray",
  },
  {
    id: 14,
    title: "Event 14",
    start: "2024-01-25T11:30:00",
    end: "2024-01-25T14:30:00",
    description:
      "Sed ante. Vivamus tortor. Duis mattis egestas metus.\n\nAenean fermentum. Donec ut mauris eget massa tempor convallis. Nulla neque libero, convallis eget, eleifend luctus, ultricies eu, nibh.",
    color: "blue",
  },
  {
    id: 15,
    title: "Event 15",
    start: "2024-01-25T11:00:00",
    end: "2024-01-25T12:00:00",
    description:
      "Vestibulum ac est lacinia nisi venenatis tristique. Fusce congue, diam id ornare imperdiet, sapien urna pretium nisl, ut volutpat sapien arcu sed augue. Aliquam erat volutpat.",
    color: "green",
  },
  {
    id: 16,
    title: "Event 16",
    start: "2024-01-26T10:55:04Z",
    end: "2024-01-26T14:08:11Z",
    description:
      "Morbi porttitor lorem id ligula. Suspendisse ornare consequat lectus. In est risus, auctor sed, tristique in, tempus sit amet, sem.\n\nFusce consequat. Nulla nisl. Nunc nisl.",
    color: "gray",
  },
  {
    id: 17,
    title: "Event 17",
    start: "2024-01-27T10:55:04Z",
    end: "2024-01-27T14:08:11Z",
    description:
      "Duis bibendum. Morbi non quam nec dui luctus rutrum. Nulla tellus.\n\nIn sagittis dui vel nisl. Duis ac nibh. Fusce lacus purus, aliquet at, feugiat non, pretium quis, lectus.",
    color: "green",
  },
  {
    id: 18,
    title: "Event 18",
    start: "2024-01-28T10:55:04Z",
    end: "2024-01-28T14:08:11Z",
    description:
      "Donec diam neque, vestibulum eget, vulputate ut, ultrices vel, augue. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Donec pharetra, magna vestibulum aliquet ultrices, erat tortor sollicitudin mi, sit amet lobortis sapien sapien non mi. Integer ac neque.\n\nDuis bibendum. Morbi non quam nec dui luctus rutrum. Nulla tellus.",
    color: "red",
  },
  {
    id: 19,
    title: "Event 19",
    start: "2024-01-29T09:38:43Z",
    end: "2024-01-29T14:19:38Z",
    description:
      "Cras non velit nec nisi vulputate nonummy. Maecenas tincidunt lacus at velit. Vivamus vel nulla eget eros elementum pellentesque.\n\nQuisque porta volutpat erat. Quisque erat eros, viverra eget, congue eget, semper rutrum, nulla. Nunc purus.",
    color: "indigo",
  },
  {
    id: 20,
    title: "Event 20",
    start: "2024-01-30T09:38:43Z",
    end: "2024-01-30T18:19:38Z",
    description:
      "Phasellus sit amet erat. Nulla tempus. Vivamus in felis eu sapien cursus vestibulum.\n\nProin eu mi. Nulla ac enim. In tempor, turpis nec euismod scelerisque, quam turpis adipiscing lorem, vitae mattis nibh ligula nec sem.",
    color: "green",
  },
];