"use client";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useState } from "react";

const emailRegExp = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
const phoneRegExp =
  /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/;
const signUpSchema = Yup.object({
  fname: Yup.string()
    .min(1, "First name must be at least 1 characters")
    .required("Please enter your first name"),
  lname: Yup.string()
    .min(1, "Last name must be at least 1 characters")
    .required("Please enter your last name"),
  mobile: Yup.string()
    .matches(phoneRegExp, "Phone number is not valid")
    .required("Please enter your phone number"),
  email: Yup.string()
    .email()
    .matches(emailRegExp, "Email is not valid")
    .required("Please enter your email"),
  password: Yup.string()
    .min(6, "Password must be at least 6 characters")
    .required("Please enter your password"),
});
const initialValues = {
  fname: "",
  lname: "",
  mobile: "",
  email: "",
  password: "",
};

export default function SignUp() {
  const router = useRouter();
  const [userNotRegistered, setUserNotRegistered] = useState("");

  const { values, errors, touched, handleBlur, handleChange, handleSubmit } =
    useFormik({
      initialValues: initialValues,
      validationSchema: signUpSchema,
      onSubmit: async (values) => {
        try {
          const userObj = {
            firstName: values.fname,
            lastName: values.lname,
            mobile: values.mobile,
            email: values.email,
            password: values.password,
          };
          const myHeaders = new Headers();
          myHeaders.append("Content-Type", "application/json");
          const requestOptions = {
            method: "POST",
            headers: myHeaders,
            body: JSON.stringify(userObj),
            redirect: "follow",
          };
          const response = await fetch(`/api/users/signup}`, requestOptions);
          const responseData = await response.json();
          if (responseData?.error === "user already exists") {
            setUserNotRegistered("user already exists");
            // console.log("user already exists");
          } else if (responseData?.error === "Invalid phone number") {
          //  console.log("invalid phone number")
          } else {
            setUserNotRegistered("");
            router.push("/profile/basic");
          }
        } catch (error) {
          console.error("Signup failed", error);
        }
      },
    });
  return (
    <>
      <div className="flex min-h-full flex-1 flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <Image
            className="mx-auto"
            src="/logo_1.svg"
            width={75}
            height={75}
            alt="Logo"
          />
          <h2 className="mt-6 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900">
            Sign up to your account
          </h2>
        </div>

        <div className="mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]">
          <div className="bg-white px-6 py-12 shadow sm:rounded-lg sm:px-12">
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div className="flex-col gap-4 sm:flex-row sm:gap-0 flex justify-between">
                <div>
                  <label
                    htmlFor="fname"
                    className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                  >
                    First Name
                  </label>
                  <div className="mt-2">
                    <input
                      id="fname"
                      name="fname"
                      type="fname"
                      autoComplete="fname"
                      placeholder="Enter First Name"
                      value={values.fname}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      className={`p-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6
                    ${
                      errors.fname && touched.fname
                        ? "border-2 border-rose-500"
                        : ""
                    }`}
                    />
                    {errors.fname && touched.fname ? (
                      <span className="form_errors text-sm font-medium leading-6 text-red-500">
                        {errors.fname}
                      </span>
                    ) : null}
                  </div>
                </div>

                <div>
                  <label
                    htmlFor="lname"
                    className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                  >
                    Last Name
                  </label>
                  <div className="mt-2">
                    <input
                      id="lname"
                      name="lname"
                      type="lname"
                      autoComplete="lname"
                      placeholder="Enter Last Name"
                      value={values.lname}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      className={`p-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6
                    ${
                      errors.lname && touched.lname
                        ? "border-2 border-rose-500"
                        : ""
                    }`}
                    />
                    {errors.lname && touched.lname ? (
                      <span className="form_errors text-sm font-medium leading-6 text-red-500">
                        {errors.lname}
                      </span>
                    ) : null}
                  </div>
                </div>
              </div>

              <div>
                <label
                  htmlFor="mobile"
                  className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                >
                  Enter Mobile Name
                </label>
                <div className="mt-2">
                  <input
                    id="mobile"
                    name="mobile"
                    type="number"
                    autoComplete="mobile"
                    placeholder="Enter Mobile Number"
                    value={values.mobile}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className={`p-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6
                  ${
                    errors.mobile && touched.mobile
                      ? "border-2 border-rose-500"
                      : ""
                  }`}
                  />
                  {errors.mobile && touched.mobile ? (
                    <span className="form_errors text-sm font-medium leading-6 text-red-500">
                      {errors.mobile}
                    </span>
                  ) : null}
                </div>
              </div>
              <div>
                <label
                  htmlFor="email"
                  className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                >
                  Email address
                </label>
                <div className="mt-2">
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    value={values.email}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    placeholder="Enter Email Password"
                    className={`p-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6
                  ${
                    errors.email && touched.email || userNotRegistered
                      ? "border-2 border-rose-500"
                      : ""
                  }`}
                  />
                  {errors.email && touched.email || userNotRegistered ? (
                    <span className="form_errors text-sm font-medium leading-6 text-red-500">
                      {userNotRegistered? userNotRegistered:errors.email}
                    </span>
                  ) : null}
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between">
                  <label
                    htmlFor="password"
                    className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                  >
                    Password
                  </label>
                </div>
                <div className="mt-2">
                  <input
                    placeholder="Enter Password"
                    id="password"
                    name="password"
                    type="password"
                    autoComplete="current-password"
                    value={values.password}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className={`p-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6
                  ${
                    errors.password && touched.password
                      ? "border-2 border-rose-500"
                      : ""
                  }`}
                  />
                  {errors.password && touched.password ? (
                    <span className="form_errors text-sm font-medium leading-6 text-red-500">
                      {errors.password}
                    </span>
                  ) : null}
                </div>
              </div>
              <div className="flex items-center justify-between">
                {/* <div className="flex items-center">
                  <input
                    id="remember-me"
                    name="remember-me"
                    type="checkbox"
                    value={values.remember}
                    onChange={handleChange}
                    className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                  />
                  <label
                    htmlFor="remember-me"
                    className="ml-3 block text-sm leading-6 text-gray-900"
                  >
                    Remember me
                  </label>
                </div> */}

                {/* <div className="text-sm leading-6">
                    <a
                      href="#"
                      className="font-semibold text-sky-500 hover:text-sky-600"
                    >
                      Forgot password?
                    </a>
                  </div> */}
              </div>

              <div>
                <button
                  type="submit"
                  className="flex w-full justify-center rounded-md bg-sky-500 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-sky-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                >
                  Sign Up
                </button>
              </div>
            </form>

            {/* <div>
                <div className="relative mt-10">
                  <div
                    className="absolute inset-0 flex items-center"
                    aria-hidden="true"
                  >
                    <div className="w-full border-t border-gray-200" />
                  </div>
                  <div className="relative flex justify-center text-sm font-medium leading-6">
                    <span className="bg-white px-6 text-gray-900">
                      Or continue with
                    </span>
                  </div>
                </div>
  
                <div className="mt-6 grid grid-cols-2 gap-4">
                  <a
                    href="#"
                    className="flex w-full items-center justify-center gap-3 rounded-md bg-[#1D9BF0] px-3 py-1.5 text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-[#1D9BF0]"
                  >
                    <svg
                      className="h-5 w-5"
                      aria-hidden="true"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84" />
                    </svg>
                    <span className="text-sm font-semibold leading-6">
                      Twitter
                    </span>
                  </a>
  
                  <a
                    href="#"
                    className="flex w-full items-center justify-center gap-3 rounded-md bg-[#24292F] px-3 py-1.5 text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-[#24292F]"
                  >
                    <svg
                      className="h-5 w-5"
                      aria-hidden="true"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="text-sm font-semibold leading-6">
                      GitHub
                    </span>
                  </a>
                </div>
              </div> */}
          </div>

          <p className="mt-10 text-center text-sm text-gray-500">
            Already a Member?{" "}
            <Link
              href="/signup"
              className="font-semibold leading-6 text-sky-500 hover:text-sky-600"
            >
              Sign In
            </Link>
          </p>
        </div>
      </div>
    </>
  );
}
