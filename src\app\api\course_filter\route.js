import API from "@/components/API";
import { verifyToken } from "@/helpers/getDataFromToken";
import { NextResponse } from "next/server";
import axios from "axios";

export async function POST(req) {
  try {
    const { classType, status, q } = await req.json();

    // console.log(classType, status, q, "backend");

    const token = req.cookies.get("coach-token")?.value || "";
    const payload = await verifyToken(token);

    let queryString = "";

    if (q) {
      queryString += `courseName=${q}`;
    }

    if (classType) {
      queryString += `${queryString ? "&" : ""}classType=${classType}`;
    }

    if (status) {
      queryString += `${queryString ? "&" : ""}status=${status}`;
    }

    const response = await axios.get(
      `${API}/api/course/?coach_id=${payload.id}&${
        queryString ? `${queryString}` : ""
      }`,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return new NextResponse(JSON.stringify(response.data));
  } catch (error) {
    console.error("error", error);
    return new NextResponse(JSON.stringify({ error: error }));
  }
}
