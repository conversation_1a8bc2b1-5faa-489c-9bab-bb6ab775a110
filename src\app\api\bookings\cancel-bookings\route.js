import API from "@/components/API";
import { verifyToken } from "@/helpers/getDataFromToken";
import { NextResponse } from "next/server";
import axios from "axios";

export async function POST(req) {
  try {
    let reqBody = await req.json();
    const token = req.cookies.get("coach-token")?.value || "";

    const resp =
      (await reqBody) &&
      reqBody.length > 0 &&
      (await Promise.all(
        reqBody.map(async (x) => {
            const response = await axios.get(
              `${API}/api/booking/${x.bookingId}`,
              {
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${token}`,
                },
              }
            );
            if (
              response &&
              response.data &&
              response.data.classes &&
              response.data.classes.length > 0
            ) {
              const filteredItem = response.data.classes.filter(
                (item) => item._id === x.classId
              );
              let obj = {
                status: "cancelled",
                sender: "coach",
                classes: filteredItem,
              };
              const result = await axios.post(
                `${API}/api/booking/cancel/${x.bookingId}`,
                { ...obj },
                {
                  headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                  },
                }
              );
              // console.log(result.data, "getting rep");
              return result.data;
          } 
        })
      ));

    // console.log(resp, "sp here");
    return new NextResponse(JSON.stringify(resp[0]));
  } catch (error) {
    console.error("error", error);
    return new NextResponse(JSON.stringify({ error: error }));
  }
}
