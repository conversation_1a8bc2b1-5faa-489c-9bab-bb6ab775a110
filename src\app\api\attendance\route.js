import API from "@/components/API";
import { verifyToken } from "@/helpers/getDataFromToken";
import { NextResponse } from "next/server";
import axios from "axios";

export async function POST(req) {
  try {
    let reqBody = await req.json();
    // console.log(reqBody);
    const token = req.cookies.get("coach-token")?.value || "";

    const response = await axios.post(
      `${API}/api/booking/markAttendanceCoach`,
      { ...reqBody },
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return new NextResponse(JSON.stringify(response.data));
  } catch (error) {
    console.error("error", error.response);
    return new NextResponse(JSON.stringify({ error: error }));
  }
}
