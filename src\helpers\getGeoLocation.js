import axios from "axios";

export const getGeoLocations = async (address) => {
  const apiKey = process.env.API_KEY_CALENDAR;
  try {
    // Construct the URL for the Geocoding API request
    const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(
      address
    )}&key=${apiKey}`;
    const response = await axios.get(url);

    // Make a GET request to the Geocoding API using axios
    if (response.data.status === "OK") {
      const result = response.data.results[0];
      const location = result.geometry.location;
      console.log(`Latitude: ${location.lat}, Longitude: ${location.lng}`);
      return {
        message: "Success",
        data: { Latitude: location.lat, Longitude: location.lng },
      };
    } else {
      console.error(
        "Geocode was not successful for the following reason",
        response.data.status
      );
    }
  } catch (error) {
    console.error("There was an error fetching the geocode data:", error);
  }
};
