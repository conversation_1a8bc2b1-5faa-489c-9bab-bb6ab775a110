"use client";

import { Bars3Icon } from "@heroicons/react/24/outline";
import { ChevronDownIcon } from "@heroicons/react/20/solid";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { usePathname } from "next/navigation";
import SuccessNotification from "../Notification/SuccessNotification";
import ErrorNotification from "../Notification/ErrorNotification";
import DeleteAccountModal from "../DeleteAccountModal/DeleteAccountModal";

function classNames(...classNamees) {
  return classNamees.filter(Boolean).join(" ");
}
export default function Header({ sidebarOpen, setSidebarOpen }) {
  const [coach, setCoach] = useState({});
  const [preview, setPreview] = useState("");
  const [show, setShow] = useState(false);

  const [successNotification, setSuccessNotification] = useState(false);
  const [errorNotification, setErrorNotification] = useState(false);
  const [message, setMessage] = useState("");
  const [open, setOpen] = useState(false);

  const router = useRouter();

  const pathname = usePathname();

  const handleLogout = async () => {
    try {
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");
      let requestOptions = {
        method: "GET",
        headers: myHeaders,
        // body: JSON.stringify(values),
      };
      const response = await fetch(`/api/logout`, requestOptions);
      const result = await response.json();
      // console.log(result, "result");
      if (!result.error) {
        router.push(`/login`);
      }
    } catch (error) {
      console.log(error);
    }
  };

  // const handleDeleteAccount = async () => {
  //   try {
  //     setShow(!show);
  //     setOpen(!open);
  //     if (userConcern) {
  //       setLoading(true);
  //       let myHeaders = new Headers();
  //       myHeaders.append("Content-Type", "application/json");

  //       let requestOptions = {
  //         method: "Post",
  //         headers: myHeaders,
  //       };

  //       const response = await fetch(`/api/delete_account`, requestOptions);

  //       const result = await response.json();
  //       console.log(result, "result");
  //       setLoading(false);
  //       setOpen(!open);
  //       if (!result.error) {
  //         setSuccessNotification(true);
  //         setMessage("Account deleted successfully");
  //         handleLogout();
  //         setTimeout(() => {
  //           setSuccessNotification(false);
  //         }, 4000);
  //       } else {
  //         setErrorNotification(true);
  //         setMessage("Account can not be deleted at the moment");
  //         setShow(!show);
  //         setTimeout(() => {
  //           setErrorNotification(false);
  //         }, 4000);
  //       }
  //     } else {
  //       setOpen(!open);
  //       setShow(!show);
  //       setLoading(false);
  //     }
  //   } catch (error) {
  //     setLoading(false);
  //     setOpen(!open);
  //     setErrorNotification(true);
  //     setMessage("Account can not be deleted at the moment");
  //     setShow(!show);
  //     setTimeout(() => {
  //       setErrorNotification(false);
  //     }, 4000);
  //     console.log(error, "error");
  //   }
  // };

  const getCoachDetails = async () => {
    try {
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let requestOptions = {
        method: "GET",
        headers: myHeaders,
      };

      const response = await fetch(`/api/coach_profile`, requestOptions);

      const result = await response.json();
      if (!result.error) {
        setCoach(result);
        await setPreview(result.profileImg);
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getCoachDetails();
  }, [pathname]);

  return (
    <>
      <DeleteAccountModal
        open={open}
        setOpen={setOpen}
        setErrorNotification={setErrorNotification}
        setMessage={setMessage}
        setSuccessNotification={setSuccessNotification}
        handleLogout={handleLogout}
      />
      {successNotification && <SuccessNotification message={message} />}

      {errorNotification && <ErrorNotification message={message} />}

      <div className="HeaderContent sticky top-0 z-40 flex h-16 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8 w-full ">
        <button
          type="button"
          className="-m-2.5 p-2.5 text-gray-700 lg:hidden cursor-pointer"
          onClick={() => setSidebarOpen(true)}
        >
          <span className="sr-only">Open sidebar</span>
          <Bars3Icon className="h-6 w-6" aria-hidden="true" />
        </button>

        {/* Separator */}
        <div className="h-6 w-px bg-gray-900/10 lg:hidden" aria-hidden="true" />

        <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
          <div className="font-semibold relative flex flex-1 items-center text-xl">
            {coach && coach.status === "active"
              ? (pathname.includes("dashboard") && "Coach Dashboard") ||
                (pathname.includes("calendar") && "Coach Calendar") ||
                (pathname.includes("list") && "Course List") ||
                (pathname.includes("course") && "Training Schedule Creation") ||
                (pathname.includes("bookings") && "Booking List") ||
                (pathname.includes("profile") && "Coach Profile") ||
                (pathname.includes("contact") && "Contact Us") ||
                (pathname.includes("reports") && "Reports")
              : "Coach Registration Form"}
          </div>
          {coach && coach.firstName && coach.firstName !== "" && (
            <div className="flex items-center gap-x-4 lg:gap-x-6 relative">
              {/* Separator */}
              <div
                className="hidden lg:block lg:h-6 lg:w-px lg:bg-gray-900/10 "
                aria-hidden="true"
              />

              {/* Profile dropdown */}
              <div className="w-full">
                <div
                  className="-m-1.5 flex items-center p-1.5 cursor-pointer"
                  onClick={() => setShow(!show)}
                >
                  <span className="sr-only">Open user menu</span>
                  <img
                    className="h-8 w-8 rounded-full bg-gray-50 object-cover"
                    src={preview}
                    alt=""
                  />
                  <span className="hidden lg:flex lg:items-center">
                    <span
                      className="ml-4 text-sm font-semibold leading-6 text-gray-900"
                      aria-hidden="true"
                    >
                      {coach.firstName} {coach.lastName}
                    </span>
                    <ChevronDownIcon
                      className="ml-2 h-5 w-5 text-gray-400"
                      aria-hidden="true"
                    />
                  </span>
                </div>
              </div>

              {/* Account Moadl */}
              {show && (
                <div className="absolute top-[60px] right-[5px] min-w-[200px]">
                  <div className="bg-white rounded overflow-hidden shadow-lg">
                    <div className="text-center p-6  border-b">
                      <img
                        className="h-24 w-24 rounded-full mx-auto object-cover"
                        src={preview}
                        alt={`${coach.firstName} ${coach.lastName}`}
                      />
                      <p className="pt-2 text-lg font-semibold">
                        {coach.firstName} {coach.lastName}
                      </p>
                      <p className="text-sm text-gray-600">{coach.email}</p>
                      <div className="mt-5">
                        <Link
                          href="/profile/basic_details"
                          className="border rounded-full py-2 px-4 text-xs font-semibold text-gray-700"
                        >
                          Manage your Account
                        </Link>
                        <div className="mt-8">
                          <div class="pl-3">
                            <p class="text-sm font-medium text-gray-800 leading-none">
                              Linked Google Account
                            </p>
                            <p class="text-xs text-gray-500 ">
                              {coach.googleEmail
                                ? coach.googleEmail
                                : "No account linked yet."}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-row justify-around">
                      <button
                        className="px-4 py-2 pb-4 w-full text-sm font-medium items-center text-center  justify-center  text-gray-800 leading-none hover:bg-gray-100 flex"
                        onClick={() => {
                          handleLogout();
                        }}
                      >
                        Logout
                      </button>
                      {/* <button
                        className="px-4 py-2 pb-4 w-full text-sm font-medium items-center text-center justify-center  text-red-800 leading-none hover:bg-red-100 flex"
                        onClick={() => {
                          setShow(!show);
                          setOpen(!open);
                        }}
                      >
                        Forget Me
                      </button> */}
                      <Link
                      onClick={()=>setShow(!show)}
                        href="/delete_account"
                        className="px-4 py-2 pb-4 w-full text-sm font-medium items-center text-center justify-center  text-red-800 leading-none hover:bg-red-100 flex"
                      >
                        Forget Me
                      </Link>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </>
  );
}
