import API from "@/components/API";
import { verifyToken } from "@/helpers/getDataFromToken";
import { NextResponse } from "next/server";
import axios from "axios";
import { getGeoLocations } from "@/helpers/getGeoLocation";

export async function PATCH(req) {
  try {
    let reqBody = await req.json();
    delete reqBody?.email;
    delete reqBody?.password;

    if (
      reqBody &&
      reqBody.linkedFacilities &&
      reqBody.linkedFacilities.length > 0
    ) {
      for (let i = 0; i < reqBody.linkedFacilities.length; i++) {
        let x = reqBody.linkedFacilities[i];
        let data = await getGeoLocations(
          `${x.name} ${x.addressLine1} ${x.city}`
        );
        if (!x.location) {
          x.location = {};
        }
        x.location.coordinates = [data.data.Latitude, data.data.Longitude];
      }
    }
    const token = req.cookies.get("coach-token")?.value || "";

    if (token !== "") {
      const payload = await verifyToken(token);

      const response = await axios.patch(
        `${API}/api/coach/${payload.id}`,
        reqBody,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      // console.log(response, "resp")

      return new NextResponse(JSON.stringify(response.data));
    } else {
      return new NextResponse(JSON.stringify({ error: "error" }));
    }
  } catch (error) {
    console.error("error 52");
    return new NextResponse(JSON.stringify({ error: error }));
  }
}

export async function GET(req) {
  try {
    const token = req.cookies.get("coach-token")?.value || "";

    if (!token) {
      return new NextResponse(JSON.stringify({ error: "No token provided" }));
    }

    const payload = await verifyToken(token);

    const response = await axios.get(`${API}/api/coach/${payload.id}`, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    // console.log(response, "resp")

    return new NextResponse(JSON.stringify(response.data));
  } catch (error) {
    console.error("error", error);
    return new NextResponse(JSON.stringify({ error: error }));
  }
}
