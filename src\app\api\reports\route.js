import API from "@/components/API";
import { verifyToken } from "@/helpers/getDataFromToken";
import { NextResponse } from "next/server";
import axios from "axios";

export async function POST(req) {
  try {
    const {  status, startDate, endDate, page } = await req.json();

    // console.log(classType, status, q, "backend");

    const token = req.cookies.get("coach-token")?.value || "";
    const payload = await verifyToken(token);

    let queryString = "";
    if (startDate) {
      queryString += `&startDate=${startDate}`;
    }

    if (endDate) {
      queryString +=  `&endDate=${endDate}`;
    }


    if (status) {
      queryString += `&paymentStatus=${status}`;
    }
    // console.log("---->>url", `${API}/api/booking/reports?page=${page}${queryString}`, token )
    // console.log(
    //   `${API}/api/booking/?coachId=${payload.id}&page=${page}${
    //     queryString ? `&${queryString}` : ""
    //   }`
    // );

    const response = await axios.post(
      `${API}/api/booking/reports?coachId=${payload.id}&page=${page}${queryString}`,{},
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );
    const responseData = {
      data: response.data,
      token: token
    };
    return new NextResponse(JSON.stringify(responseData));
  } catch (error) {
    console.error("error", error.response.data);
    return new NextResponse(JSON.stringify({ error: error }));
  }
}
