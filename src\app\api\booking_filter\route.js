import API from "@/components/API";
import { verifyToken } from "@/helpers/getDataFromToken";
import { NextResponse } from "next/server";
import axios from "axios";

export async function POST(req) {
  try {
    const { classType, status, q, startDate, endDate, page } = await req.json();

    // console.log(classType, status, q, "backend");

    const token = req.cookies.get("coach-token")?.value || "";
    const payload = await verifyToken(token);

    let queryString = "";

    if (q) {
      queryString += `courseName=${q}`;
    }

    if (startDate) {
      queryString += `${queryString ? "&" : ""}startDate=${startDate}`;
    }

    if (endDate) {
      queryString += `${queryString ? "&" : ""}endDate=${endDate}`;
    }

    if (classType) {
      queryString += `${queryString ? "&" : ""}courseType=${classType}`;
    }

    if (status) {
      queryString += `${queryString ? "&" : ""}status=${status}`;
    }

    // console.log(
    //   `${API}/api/booking/?coachId=${payload.id}&page=${page}${
    //     queryString ? `&${queryString}` : ""
    //   }`
    // );
    const response = await axios.get(
      `${API}/api/booking/?coachId=${payload.id}&page=${page}${
        queryString ? `&${queryString}` : ""
      }`,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );
    // console.log(response.data, "pppppp");
    return new NextResponse(JSON.stringify(response.data));
  } catch (error) {
    console.error("error", error);
    return new NextResponse(JSON.stringify({ error: error }));
  }
}
