import API from "@/components/API";
import { verifyToken } from "@/helpers/getDataFromToken";
import axios from "axios";

// import { cookies } from "next/headers";
import { NextResponse } from "next/server";

export async function POST(req, res) {
  try {
    const reqBody = await req.json();
    const response = await axios.post(
      `${API}/api/coach/requestResetPassword/${reqBody.email}`,
      reqBody,
      {
        headers: { "Content-Type": "application/json" },
      }
    );
    return new NextResponse(JSON.stringify(response.data));
  } catch (error) {
    console.error(error?.response?.data?.error);
    return new NextResponse(JSON.stringify({ error: error?.response?.data?.error }));
  }
}
