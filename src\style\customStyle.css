.react-calendar {
  border-radius: 5px;
  width: 370px;
}
.required:after {
  content: " *";
  color: red;
}
span.react-calendar__navigation__label__labelText.react-calendar__navigation__label__labelText--from {
  font-size: 18px;
}
.react-calendar__tile {
  border: 0.7px solid #d5d4df !important;
  display: flex;
  align-items: center;
  justify-content: center;
}
.react-calendar__month-view__weekdays__weekday abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
  text-decoration: none;
  text-transform: capitalize;
  font-weight: 400;
  color: #898686;
  font-size: 14px;
}
button.react-calendar__navigation__arrow.react-calendar__navigation__next-button,
button.react-calendar__navigation__arrow.react-calendar__navigation__prev-button {
  font-size: 25px;
}
.react-calendar__month-view__days__day--neighboringMonth {
  background: #f2f3f7 !important;
}
.react-calendar__month-view__days__day--neighboringMonth abbr {
  color: #a8a8a8 !important;
}
.react-calendar__tile--now {
  background: transparent;
}
.react-calendar__month-view__days__day--weekend,
.react-calendar__tile abbr {
  font-size: 15px;
  color: #000;
  font-weight: 300;
}
.react-calendar__tile--hasActive {
  background: transparent;
}
button.react-calendar__tile.react-calendar__tile--active.react-calendar__tile--range.react-calendar__tile--rangeStart.react-calendar__tile--rangeEnd.react-calendar__tile--rangeBothEnds.react-calendar__month-view__days__day
  abbr,
.react-calendar__month-view__days .react-calendar__tile--hasActive abbr {
  border-radius: 100px;
  border-radius: 100px;
  background: #006edc;
  min-width: 34px !important;
  color: #fff !important;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 34px;
}
.react-calendar__tile--active {
  background-color: transparent;
}

.react-calendar__tile--hasActive:enabled:hover,
.react-calendar__tile--hasActive:enabled:focus {
  background: transparent !important;
}
.react-calendar__tile:enabled:hover,
.react-calendar__tile:enabled:focus {
  background-color: transparent;
}
.react-calendar button {
  min-height: 56px;
}

.react-calendar__month-view__days button:first-child {
  border-radius: 5px 0 0 0;
}
.react-calendar__month-view__days button:nth-child(7) {
  border-radius: 0 5px 0 0;
}
.react-calendar__month-view__days button:nth-child(29) {
  border-radius: 0 0 0 5px;
}
.react-calendar__month-view__days button:nth-child(35) {
  border-radius: 0 0 5px 0;
}

.removeScroller::-webkit-scrollbar {
  display: none;
}

.react-datepicker__time-container {
  position: absolute;
  top: 40px;
  width: 125px;
  left: 40%;
  border: 1px solid lightgray;
  background: #fff;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
ul.react-datepicker__time-list {
  height: 215px;
  overflow: auto;
  background: #fff;
  min-width: 100px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 5px 15px;
}
ul.react-datepicker__time-list li {
  cursor: pointer;
}
.react-datepicker__header.react-datepicker__header--time.react-datepicker__header--time--only {
  font-size: 16px;
  padding: 15px 5px;
  width: 100%;
  background: lightgray;
  font-weight: 700;
  border-bottom: 0.5px solid lightgray;
  align-items: center;
}
li.react-datepicker__time-list-item--disabled {
  opacity: 0.8;
  cursor: not-allowed;
}
