export const AlertModal = ({ show, message, onClose }) => {
    if (!show) return null;
    return (
      <div className="fixed inset-0 flex items-center justify-center z-50">
        <div className="absolute inset-0 bg-black opacity-50"></div>
        <div className="bg-white p-6 rounded-lg shadow-lg z-10 w-1/4">
          <h2 className="text-xl font-semibold mb-4">Alert</h2>
          <p className="mb-4">{message}</p>
          <div className="flex justify-end">
            <button
              className="px-4 py-2 bg-blue-500 text-white rounded"
              onClick={onClose}
            >
              OK
            </button>
          </div>
        </div>
      </div>
    );
  };
  