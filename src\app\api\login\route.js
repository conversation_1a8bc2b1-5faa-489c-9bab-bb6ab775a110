import API from "@/components/API";
import { verifyToken } from "@/helpers/getDataFromToken";
import axios from "axios";

// import { cookies } from "next/headers";
import { NextResponse } from "next/server";

export async function POST(req, res) {
  try {
    const reqBody = await req.json();
    const response = await axios.post(`${API}/api/coach/login`, reqBody, {
      headers: { "Content-Type": "application/json" },
    });
    // console.log(response.data, "response");

    const token = response.data?.token;

    const resp = NextResponse.json({
      message: "Login Success",
      success: true,
    });

    resp.cookies.set("coach-token", token, { httpOnly: true });
    const payload = await verifyToken(token);
    resp.cookies.set("id", payload.id, { httpOnly: true, maxAge: 86400 });
    return resp;
  } catch (error) {
    console.error(error);
    return new NextResponse(JSON.stringify({ error: error }));
  }
}
