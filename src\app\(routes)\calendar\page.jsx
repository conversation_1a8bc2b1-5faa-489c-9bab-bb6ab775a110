"use client";

import Calendar from "@/components/Calendar/Calendar";
import GoogleSignIn from "@/components/Calendar/GoogleSignIn";
import NewCalendar from "@/components/Calendar/NewCalendar";
import { useState, useEffect } from "react";

export default function CalendarPage() {
  const [googleAccounrLinked, setGoogleAccountLinked] = useState(false);
  const [loading, setLoading] = useState(true);
  const [coachDetails, setCoachDetails] = useState({});

  const getCoachDetails = async () => {
    try {
      setLoading(true);
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let requestOptions = {
        method: "GET",
        headers: myHeaders,
      };

      const response = await fetch(`/api/coach_profile`, requestOptions);

      const result = await response.json();
      if (!result.error) {
        setCoachDetails(result);
        if (!result?.refreshToken || result.refreshToken !== "") {
          setGoogleAccountLinked(false);
          setLoading(false);
        }
        if (result.refreshToken) {
          setGoogleAccountLinked(true);
          setLoading(false);
        }
      }
    } catch (error) {
      console.log(error); 
      setLoading(false);
    }
  };

  useEffect(() => {
    getCoachDetails();
  }, []);

  return (
    <>
    <title>Coach Calendar</title>
      {loading ? (
        <div className="flex items-center justify-center h-screen">
          <div
            className="inline-block h-20 w-20 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"
            role="status"
          >
            <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">
              Loading...
            </span>
          </div>
        </div>
      ) : googleAccounrLinked ? (
        <Calendar />
      ) : (
        <GoogleSignIn user={coachDetails} />
      )}
    </>
  );
}
