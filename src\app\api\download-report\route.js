import API from "@/components/API";
import { verifyToken } from "@/helpers/getDataFromToken";
import { NextResponse } from "next/server";
import axios from "axios";

export async function POST(req) {
  try {
    const { status, startDate, endDate, page } = await req.json();

    console.log(status, startDate, endDate, page, "backend");

    const token = req.cookies.get("coach-token")?.value || "";
    const payload = await verifyToken(token);

    let queryString = "";
    if (startDate) {
      queryString += `&startDate=${startDate}`;
    }

    if (endDate) {
      queryString += `&endDate=${endDate}`;
    }

    if (status) {
      queryString += `&paymentStatus=${status}`;
    }

    const response = await axios.post(
      `${API}/api/booking/downloadReports?coachId=${payload.id}&page=${page}${queryString}`,
      {},
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );

    return new NextResponse(JSON.stringify(response.data));
  } catch (error) {
    console.error("error", error);
    return new NextResponse(JSON.stringify({ error: error }));
  }
}
