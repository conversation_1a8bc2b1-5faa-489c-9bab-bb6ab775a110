import API from "@/components/API";
import { verifyToken } from "@/helpers/getDataFromToken";
import { NextResponse } from "next/server";
import axios from "axios";

export async function POST(req) {
  try {
    let reqBody = await req.json();

    const token = req.cookies.get("coach-token")?.value || "";

    const payload = await verifyToken(token);
    const coachDetail = await axios.get(`${API}/api/coach/${payload.id}`, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });
    const coachName = `${coachDetail.data.firstName} ${coachDetail.data.lastName}`;
    reqBody = { ...reqBody, coach_id: payload.id, coachName };

    // console.log(reqBody, "pp");

    const response = await axios.post(`${API}/api/course/`, reqBody, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    // console.log("---res", coachDetail);
    return new NextResponse(JSON.stringify(response.data));
  } catch (error) {
    console.error("error", error);
    return new NextResponse(JSON.stringify({ error: error }));
  }
}

export async function PATCH(req) {
  try {
    let courseData = await req.json();

    const token = req.cookies.get("coach-token")?.value || "";
    const payload = await verifyToken(token);
    const coachDetail = await axios.get(`${API}/api/coach/${payload.id}`, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });
    const coachName = `${coachDetail.data.firstName} ${coachDetail.data.lastName}`;
    courseData = {
      ...courseData,
      coach_id: payload.id,
      coachName,
    };
    const response = await axios.patch(
      `${API}/api/course/${courseData.courseId}`,
      courseData,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );
    // console.log(response.data);
    return new NextResponse(JSON.stringify(response.data));
  } catch (error) {
    console.error("error", error);
    return new NextResponse(JSON.stringify({ error: error }));
  }
}
