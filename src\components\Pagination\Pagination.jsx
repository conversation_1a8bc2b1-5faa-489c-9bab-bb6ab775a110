import React from "react";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/20/solid";
import Link from "next/link";

const Pagination = ({totalResults, currentPage, totalPages, selectedPage, setSelectedPage}) => {
  // Calculate the start and end page numbers
  let startPage = Math.max(1, currentPage - 5);
  let endPage = Math.min(totalPages, startPage + 9);

  // Adjust the start and end page numbers if there are less than 10 pages
  if (endPage - startPage < 9) {
    startPage = Math.max(1, endPage - 9);
  }

  // Create an array of page numbers to display
  const pages = Array.from(
    { length: endPage - startPage + 1 },
    (_, i) => startPage + i
  );


  return (
    <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
      <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
        <div>
          <p className="text-sm text-gray-700">
            Showing{" "}
            <span className="font-medium">{(currentPage - 1) * 25 + 1}</span> to{" "}
            <span className="font-medium">
              {Math.min(currentPage * 25, totalResults)}
            </span>{" "}
            of <span className="font-medium">{totalResults}</span> results
          </p>
        </div>
        <div>
          <nav
            className="isolate inline-flex -space-x-px rounded-md shadow-sm"
            aria-label="Pagination"
          >
            <Link
              href=""
              onClick={() => selectedPage === 1 ? null : setSelectedPage(selectedPage - 1)}
              className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"
            >
              <span className="sr-only">Previous</span>
              <ChevronLeftIcon className="h-5 w-5" aria-hidden="true" />
            </Link>
            {pages.map((page) => (
              <Link
                key={page}
                href=""
                onClick={() => setSelectedPage(page)}
                aria-current={page === currentPage ? "page" : undefined}
                className={`${
                  page === selectedPage
                    ? "z-10 bg-indigo-600 text-white focus:outline focus:ring-2 focus:ring-indigo-600 focus:ring-opacity-50"
                    : "text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:outline-offset-0"
                } relative inline-flex items-center px-4 py-2 text-sm font-semibold`}
              >
                {page}
              </Link>
            ))}
            <Link
              href=""
              onClick={() => pages.length > 1 ? setSelectedPage(selectedPage + 1) : null}
              className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"
            >
              <span className="sr-only">Next</span>
              <ChevronRightIcon className="h-5 w-5" aria-hidden="true" />
            </Link>
          </nav>
        </div>
      </div>
    </div>
  );
};

export default Pagination;
