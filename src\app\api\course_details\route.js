import API from "@/components/API";
import { verifyToken } from "@/helpers/getDataFromToken";
import { NextResponse } from "next/server";
import axios from "axios";


export async function POST(req) {
  try {

     let courseId = await req.json();

    const token = req.cookies.get("coach-token")?.value || "";
    const payload = await verifyToken(token);

    const response = await axios.get(`${API}/api/course/${courseId}`, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });
    // console.log(response.data);
    return new NextResponse(JSON.stringify(response.data));
  } catch (error) {
    console.error("error", error);
    return new NextResponse(JSON.stringify({ error: error }));
  }
}


