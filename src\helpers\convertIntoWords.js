export function numberToWords(num) {
  if (num === 0) return "zero";

  const belowTwenty = [
    "zero",
    "one",
    "two",
    "three",
    "four",
    "five",
    "six",
    "seven",
    "eight",
    "nine",
    "ten",
    "eleven",
    "twelve",
    "thirteen",
    "fourteen",
    "fifteen",
    "sixteen",
    "seventeen",
    "eighteen",
    "nineteen",
  ];
  const tens = [
    "",
    "",
    "twenty",
    "thirty",
    "forty",
    "fifty",
    "sixty",
    "seventy",
    "eighty",
    "ninety",
  ];
  const thousands = ["thousand", "million", "billion"];

  function helper(n) {
    if (n < 20) return belowTwenty[n];
    if (n < 100)
      return (
        tens[Math.floor(n / 10)] +
        (n % 10 !== 0 ? " " + belowTwenty[n % 10] : "")
      );
    if (n < 1000)
      return (
        belowTwenty[Math.floor(n / 100)] +
        " hundred" +
        (n % 100 !== 0 ? " " + helper(n % 100) : "")
      );

    for (let i = 0; i < thousands.length; i++) {
      let unit = 1000 ** (i + 1);
      if (n < unit * 1000) {
        return (
          helper(Math.floor(n / unit)) +
          " " +
          thousands[i] +
          (n % unit !== 0 ? " " + helper(n % unit) : "")
        );
      }
    }
  }

  // Split the number into whole and decimal parts
  const [wholePart, decimalPart] = num.toString().split(".");

  let words = helper(parseInt(wholePart));

  // Convert the decimal part to words if it exists and is not all zeros
  if (decimalPart && parseInt(decimalPart) !== 0) {
    const decimalWords = decimalPart
      .split("")
      .map((digit) => belowTwenty[parseInt(digit)])
      .join(" ");
    words += " point " + decimalWords;
  }

  return words;
}
