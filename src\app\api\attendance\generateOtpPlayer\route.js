import API from "@/components/API";
import { verifyToken } from "@/helpers/getDataFromToken";
import { NextResponse } from "next/server";
import axios from "axios";

export async function POST(req) {
  try {
    const reqBody = await req.json();
    
    const token = req.cookies.get("coach-token")?.value || "";

    const response = await axios.post(
      `${API}/api/booking/generateOtpPlayer`,
      reqBody,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );

    return new NextResponse(JSON.stringify(response.data));
  } catch (error) {
    console.error("OTP generation error", error);
    return new NextResponse(
      JSON.stringify({ 
        error: error.response?.data?.error || "Failed to generate OTP" 
      }),
      { status: error.response?.status || 500 }
    );
  }
}